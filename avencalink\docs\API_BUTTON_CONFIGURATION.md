# API-Based Button Configuration

This document explains how to configure buttons and components using API data in the AvencaLink application.

## Overview

The application now supports defining button texts, styles, and behaviors directly from the API data structure, allowing for dynamic customization of user interfaces without code changes.

## Button Configuration Structure

### ButtonConfig Interface

```typescript
interface ButtonConfig {
  primaryButtonText?: string;
  secondaryButtonText?: string;
  showBadge?: boolean;
}
```

### Section Interfaces

Each section that supports buttons now includes an optional `buttonConfig` property:

```typescript
interface FeaturesSection {
  title: string;
  description: string;
  enabled: boolean;
  items: SectionItem[];
  buttonConfig?: ButtonConfig; // New optional property
}

interface ServicesSection {
  title: string;
  description: string;
  enabled: boolean;
  items: SectionItem[];
  buttonConfig?: ButtonConfig; // New optional property
}

interface GenericSection {
  title: string;
  description: string;
  enabled: boolean;
  items: SectionItem[];
  buttonConfig?: ButtonConfig; // New optional property
}
```

## Default Button Configurations

The system provides default button configurations for each section type:

```typescript
const SECTION_BUTTON_CONFIG = {
  features: {
    primaryButtonText: "Ver",
    secondaryButtonText: "Contato",
    showBadge: false,
  },
  services: {
    primaryButtonText: "Solicitar",
    secondaryButtonText: "Info",
    showBadge: true,
  },
  generic: {
    primaryButtonText: "Ver Mais",
    secondaryButtonText: "Contato",
    showBadge: false,
  },
};
```

## API Data Examples

### Features Section with Custom Button Configuration

```json
{
  "featuresSection": {
    "title": "My Features",
    "description": "Check out these amazing features",
    "enabled": true,
    "buttonConfig": {
      "primaryButtonText": "Explore Feature",
      "secondaryButtonText": "Learn More",
      "showBadge": true
    },
    "items": [
      {
        "id": 1,
        "title": "Custom Feature",
        "description": "Feature description",
        "image": "https://example.com/image.jpg",
        "primaryButton": {
          "icon": "fa fa-eye",
          "url": "https://example.com/feature"
        },
        "secondaryButton": {
          "icon": "fa fa-info-circle",
          "url": "https://example.com/info"
        }
      }
    ]
  }
}
```

### Services Section with Custom Button Configuration

```json
{
  "servicesSection": {
    "title": "My Services",
    "description": "Professional services",
    "enabled": true,
    "buttonConfig": {
      "primaryButtonText": "Book Service",
      "secondaryButtonText": "Get Quote",
      "showBadge": false
    },
    "items": [
      {
        "id": 1,
        "title": "Custom Service",
        "description": "Service description",
        "image": "https://example.com/service.jpg",
        "primaryButton": {
          "icon": "fa fa-calendar",
          "url": "https://example.com/book"
        },
        "secondaryButton": {
          "icon": "fa fa-phone",
          "url": "https://wa.me/5511999999999"
        }
      }
    ]
  }
}
```

### Generic Section Using Default Configuration

```json
{
  "genericSection": {
    "title": "Additional Content",
    "description": "Generic content",
    "enabled": true,
    "items": [
      {
        "id": 1,
        "title": "Generic Item",
        "description": "This will use default button configuration",
        "image": "https://example.com/generic.jpg",
        "primaryButton": {
          "icon": "fa fa-eye",
          "url": "https://example.com/view"
        },
        "secondaryButton": {
          "icon": "fa fa-phone",
          "url": "https://wa.me/5511999999999"
        }
      }
    ]
  }
}
```

## How It Works

1. **Priority System**: API-provided button configuration takes priority over default configurations
2. **Fallback Mechanism**: If no `buttonConfig` is provided, the system uses default values based on section type
3. **Validation**: All button configurations are validated to ensure data integrity
4. **Dynamic Application**: Button configurations are applied at runtime using the `getButtonConfig()` utility function

## Implementation Details

### Button Configuration Utility

The `getButtonConfig()` function merges API data with defaults:

```typescript
function getButtonConfig(
  sectionType: "features" | "services" | "generic",
  apiButtonConfig?: ButtonConfig
): Required<ButtonConfig> {
  const defaultConfig = SECTION_BUTTON_CONFIG[sectionType];

  return {
    primaryButtonText:
      apiButtonConfig?.primaryButtonText ?? defaultConfig.primaryButtonText,
    secondaryButtonText:
      apiButtonConfig?.secondaryButtonText ?? defaultConfig.secondaryButtonText,
    showBadge: apiButtonConfig?.showBadge ?? defaultConfig.showBadge,
  };
}
```

### Component Usage

In the UserProfile component, button configurations are applied using the spread operator:

```typescript
<GenericSection
  sectionData={profile.featuresSection}
  layout="carousel"
  {...getButtonConfig("features", profile.featuresSection.buttonConfig)}
  sectionType="features"
/>
```

## Validation

Button configurations are validated using the `validateButtonConfig()` function:

```typescript
function validateButtonConfig(config: ButtonConfig): ValidationResult {
  const errors: ValidationError[] = [];

  if (
    config.primaryButtonText !== undefined &&
    typeof config.primaryButtonText !== "string"
  ) {
    errors.push({
      field: "primaryButtonText",
      message: "Primary button text must be a string",
    });
  }

  if (
    config.secondaryButtonText !== undefined &&
    typeof config.secondaryButtonText !== "string"
  ) {
    errors.push({
      field: "secondaryButtonText",
      message: "Secondary button text must be a string",
    });
  }

  if (config.showBadge !== undefined && typeof config.showBadge !== "boolean") {
    errors.push({
      field: "showBadge",
      message: "Show badge must be a boolean",
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
```

## Benefits

1. **Dynamic Customization**: Change button texts without code deployment
2. **Multilingual Support**: Different button texts for different languages/regions
3. **A/B Testing**: Test different button texts for conversion optimization
4. **Brand Consistency**: Maintain consistent button styling across different sections
5. **Flexibility**: Override defaults only when needed, fall back to sensible defaults

## Migration Guide

Existing API data will continue to work without changes. To add custom button configuration:

1. Add the optional `buttonConfig` property to your section data
2. Specify the desired button texts and badge visibility
3. The system will automatically use your custom configuration

## Example Files

See `public/data/sample-user-with-button-config.json` for a complete example of API data with button configuration.
