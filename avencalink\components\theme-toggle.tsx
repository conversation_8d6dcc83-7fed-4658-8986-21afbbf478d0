"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import { cn } from "@/lib/utils";

interface ThemeToggleProps {
  className?: string;
}

export function ThemeToggle({ className }: ThemeToggleProps) {
  const { setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Render a static placeholder to prevent layout shift and hydration errors
    return <div className={cn("h-8 w-16 rounded-full", className)} />;
  }

  const isDark = resolvedTheme === "dark";

  // const spring = {
  //   type: "spring",
  //   stiffness: 700,
  //   damping: 30,
  // };

  return (
    <div
      className={cn(
        "flex h-8 w-16 cursor-pointer items-center rounded-full p-1",
        isDark
          ? "justify-start border border-zinc-800 bg-zinc-950"
          : "justify-end border border-zinc-200 bg-gray-50",
        className
      )}
      onClick={() => setTheme(isDark ? "light" : "dark")}
      role="button"
      tabIndex={0}
    >
      <span className="sr-only">Toggle theme</span>
      <motion.div
        className={cn(
          "flex h-6 w-6 items-center justify-center rounded-full",
          isDark ? "bg-zinc-800" : "bg-gray-200"
        )}
        layout
        transition={{ duration: 0.5 }}
      >
        {isDark ? (
          <Moon className="h-4 w-4 text-white" strokeWidth={1.5} />
        ) : (
          <Sun className="h-4 w-4 text-gray-700" strokeWidth={1.5} />
        )}
      </motion.div>
    </div>
  );
}
