"use client";

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import { cn } from "@/lib/utils";

interface ThemeToggleProps {
  className?: string;
  variant?: "default" | "compact" | "icon-only";
}

export function ThemeToggle({
  className,
  variant = "default",
}: ThemeToggleProps) {
  const { setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Render a static placeholder to prevent layout shift and hydration errors
    return (
      <div
        className={cn(
          variant === "icon-only" ? "h-9 w-9" : "h-8 w-16",
          "rounded-full bg-muted animate-pulse",
          className
        )}
      />
    );
  }

  const isDark = resolvedTheme === "dark";

  const spring = {
    type: "spring",
    stiffness: 700,
    damping: 30,
  };

  const iconVariants = {
    initial: { scale: 0, rotate: -180 },
    animate: { scale: 1, rotate: 0 },
    exit: { scale: 0, rotate: 180 },
  };

  if (variant === "icon-only") {
    return (
      <motion.button
        className={cn(
          "relative flex h-9 w-9 items-center justify-center rounded-lg border bg-background shadow-sm transition-all duration-200 hover:bg-accent hover:shadow-md focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          className
        )}
        onClick={() => setTheme(isDark ? "light" : "dark")}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        aria-label={`Switch to ${isDark ? "light" : "dark"} mode`}
      >
        <AnimatePresence mode="wait" initial={false}>
          {isDark ? (
            <motion.div
              key="moon"
              variants={iconVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={spring}
            >
              <Moon className="h-4 w-4" strokeWidth={1.5} />
            </motion.div>
          ) : (
            <motion.div
              key="sun"
              variants={iconVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={spring}
            >
              <Sun className="h-4 w-4" strokeWidth={1.5} />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.button>
    );
  }

  return (
    <motion.div
      className={cn(
        "relative flex h-8 w-16 cursor-pointer items-center rounded-full border p-1 transition-all duration-300 hover:shadow-md",
        isDark
          ? "justify-start border-border bg-muted"
          : "justify-end border-border bg-background",
        className
      )}
      onClick={() => setTheme(isDark ? "light" : "dark")}
      role="button"
      tabIndex={0}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          setTheme(isDark ? "light" : "dark");
        }
      }}
      aria-label={`Switch to ${isDark ? "light" : "dark"} mode`}
    >
      <span className="sr-only">Toggle theme</span>
      <motion.div
        className={cn(
          "flex h-6 w-6 items-center justify-center rounded-full shadow-sm",
          isDark ? "bg-primary" : "bg-background border border-border"
        )}
        layout
        transition={spring}
      >
        <AnimatePresence mode="wait" initial={false}>
          {isDark ? (
            <motion.div
              key="moon"
              variants={iconVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={spring}
            >
              <Moon
                className="h-3 w-3 text-primary-foreground"
                strokeWidth={2}
              />
            </motion.div>
          ) : (
            <motion.div
              key="sun"
              variants={iconVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={spring}
            >
              <Sun className="h-3 w-3 text-foreground" strokeWidth={2} />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
}
