{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatUrl(url: string): string {\n  if (!url) return \"\"\n  \n  // Add https:// if no protocol is specified\n  if (!url.startsWith(\"http://\") && !url.startsWith(\"https://\")) {\n    return `https://${url}`\n  }\n  \n  return url\n}\n\nexport function validateUsername(username: string): boolean {\n  // Username validation: alphanumeric, underscores, hyphens, 3-30 characters\n  const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/\n  return usernameRegex.test(username)\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.substring(0, maxLength) + \"...\"\n}\n\nexport function generateMetaTitle(username: string, displayName?: string): string {\n  const name = displayName || username\n  return `${name} | AvencaLink`\n}\n\nexport function generateMetaDescription(bio?: string, username?: string): string {\n  if (bio) {\n    return truncateText(bio, 160)\n  }\n  return `Check out ${username}'s links on AvencaLink`\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,UAAU,GAAW;IACnC,IAAI,CAAC,KAAK,OAAO;IAEjB,2CAA2C;IAC3C,IAAI,CAAC,IAAI,UAAU,CAAC,cAAc,CAAC,IAAI,UAAU,CAAC,aAAa;QAC7D,OAAO,CAAC,QAAQ,EAAE,KAAK;IACzB;IAEA,OAAO;AACT;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,2EAA2E;IAC3E,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,kBAAkB,QAAgB,EAAE,WAAoB;IACtE,MAAM,OAAO,eAAe;IAC5B,OAAO,GAAG,KAAK,aAAa,CAAC;AAC/B;AAEO,SAAS,wBAAwB,GAAY,EAAE,QAAiB;IACrE,IAAI,KAAK;QACP,OAAO,aAAa,KAAK;IAC3B;IACA,OAAO,CAAC,UAAU,EAAE,SAAS,sBAAsB,CAAC;AACtD", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 active:bg-primary/95\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 active:bg-destructive/95 focus-visible:ring-destructive\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground active:bg-accent/80\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 active:bg-secondary/90\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground active:bg-accent/80\",\n        link: \"text-primary underline-offset-4 hover:underline focus-visible:ring-0 focus-visible:ring-offset-0\",\n        success:\n          \"bg-success text-success-foreground shadow-sm hover:bg-success/90 active:bg-success/95 focus-visible:ring-success\",\n        warning:\n          \"bg-warning text-warning-foreground shadow-sm hover:bg-warning/90 active:bg-warning/95 focus-visible:ring-warning\",\n        info: \"bg-info text-info-foreground shadow-sm hover:bg-info/90 active:bg-info/95 focus-visible:ring-info\",\n        gradient:\n          \"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-sm hover:from-primary/90 hover:to-primary/70 active:from-primary/95 active:to-primary/75\",\n      },\n      size: {\n        xs: \"h-7 px-2 text-xs gap-1\",\n        sm: \"h-8 px-3 text-sm gap-1.5\",\n        default: \"h-9 px-4 py-2\",\n        lg: \"h-10 px-6 text-base\",\n        xl: \"h-12 px-8 text-lg\",\n        icon: \"size-9\",\n        \"icon-sm\": \"size-8\",\n        \"icon-lg\": \"size-10\",\n        \"icon-xl\": \"size-12\",\n      },\n      animation: {\n        none: \"\",\n        hover: \"hover:scale-105 active:scale-95\",\n        lift: \"hover:-translate-y-0.5 hover:shadow-lg active:translate-y-0\",\n        glow: \"hover:shadow-lg hover:shadow-primary/25\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      animation: \"none\",\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n  isLoading?: boolean;\n  loadingText?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  (\n    {\n      className,\n      variant,\n      size,\n      animation,\n      asChild = false,\n      isLoading = false,\n      loadingText,\n      leftIcon,\n      rightIcon,\n      children,\n      disabled,\n      ...props\n    },\n    ref\n  ) => {\n    const Comp = asChild ? Slot : \"button\";\n    const isDisabled = disabled || isLoading;\n\n    return (\n      <Comp\n        ref={ref}\n        data-slot=\"button\"\n        className={cn(buttonVariants({ variant, size, animation, className }))}\n        disabled={isDisabled}\n        aria-disabled={isDisabled}\n        {...props}\n      >\n        {isLoading && (\n          <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent\" />\n        )}\n        {!isLoading && leftIcon && leftIcon}\n        {isLoading ? loadingText || children : children}\n        {!isLoading && rightIcon && rightIcon}\n      </Comp>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,ugBACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;YACN,SACE;YACF,SACE;YACF,MAAM;YACN,UACE;QACJ;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,WAAW;YACX,WAAW;YACX,WAAW;QACb;QACA,WAAW;YACT,MAAM;YACN,OAAO;YACP,MAAM;YACN,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,WAAW;IACb;AACF;AAaF,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC5B,CACE,EACE,SAAS,EACT,OAAO,EACP,IAAI,EACJ,SAAS,EACT,UAAU,KAAK,EACf,YAAY,KAAK,EACjB,WAAW,EACX,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,aAAa,YAAY;IAE/B,qBACE,6WAAC;QACC,KAAK;QACL,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;YAAW;QAAU;QACnE,UAAU;QACV,iBAAe;QACd,GAAG,KAAK;;YAER,2BACC,6WAAC;gBAAI,WAAU;;;;;;YAEhB,CAAC,aAAa,YAAY;YAC1B,YAAY,eAAe,WAAW;YACtC,CAAC,aAAa,aAAa;;;;;;;AAGlC;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/app/%5Busername%5D/not-found.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { Button } from '@/components/ui/button'\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n      <div className=\"text-center px-4\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-6xl font-bold text-gray-900 dark:text-white mb-4\">\n            404\n          </h1>\n          <h2 className=\"text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4\">\n            Profile Not Found\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400 max-w-md mx-auto mb-8\">\n            The profile you&apos;re looking for doesn&apos;t exist or has been deactivated.\n            Please check the username and try again.\n          </p>\n        </div>\n\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Button asChild>\n            <Link href=\"/\">\n              Go Home\n            </Link>\n          </Button>\n          <Button variant=\"outline\" asChild>\n            <Link href=\"/signup\">\n              Create Your Profile\n            </Link>\n          </Button>\n        </div>\n\n        <div className=\"mt-12 text-center\">\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n            Want to claim this username?{' '}\n            <Link \n              href=\"/signup\" \n              className=\"text-blue-600 hover:text-blue-500 underline\"\n            >\n              Sign up now\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6WAAC;4BAAG,WAAU;sCAA+D;;;;;;sCAG7E,6WAAC;4BAAE,WAAU;sCAAyD;;;;;;;;;;;;8BAMxE,6WAAC;oBAAI,WAAU;;sCACb,6WAAC,2HAAA,CAAA,SAAM;4BAAC,OAAO;sCACb,cAAA,6WAAC,2RAAA,CAAA,UAAI;gCAAC,MAAK;0CAAI;;;;;;;;;;;sCAIjB,6WAAC,2HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,OAAO;sCAC/B,cAAA,6WAAC,2RAAA,CAAA,UAAI;gCAAC,MAAK;0CAAU;;;;;;;;;;;;;;;;;8BAMzB,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAE,WAAU;;4BAA2C;4BACzB;0CAC7B,6WAAC,2RAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}