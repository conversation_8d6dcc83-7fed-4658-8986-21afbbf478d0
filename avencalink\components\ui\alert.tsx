import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const alertVariants = cva(
  "relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground border-border",
        destructive:
          "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive bg-destructive/10",
        success:
          "border-success/50 text-success dark:border-success [&>svg]:text-success bg-success/10",
        warning:
          "border-warning/50 text-warning dark:border-warning [&>svg]:text-warning bg-warning/10",
        info:
          "border-info/50 text-info dark:border-info [&>svg]:text-info bg-info/10",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>
>(({ className, variant, ...props }, ref) => (
  <div
    ref={ref}
    role="alert"
    className={cn(alertVariants({ variant }), className)}
    {...props}
  />
))
Alert.displayName = "Alert"

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h5
    ref={ref}
    className={cn("mb-1 font-medium leading-none tracking-tight", className)}
    {...props}
  />
))
AlertTitle.displayName = "AlertTitle"

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm [&_p]:leading-relaxed", className)}
    {...props}
  />
))
AlertDescription.displayName = "AlertDescription"

// Enhanced Alert with icons and actions
interface EnhancedAlertProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: VariantProps<typeof alertVariants>['variant']
  title?: string
  description?: string
  icon?: React.ReactNode
  action?: React.ReactNode
  dismissible?: boolean
  onDismiss?: () => void
}

const EnhancedAlert = React.forwardRef<HTMLDivElement, EnhancedAlertProps>(
  ({ 
    className, 
    variant = "default", 
    title, 
    description, 
    icon, 
    action, 
    dismissible = false,
    onDismiss,
    children,
    ...props 
  }, ref) => {
    const [isVisible, setIsVisible] = React.useState(true)

    const handleDismiss = () => {
      setIsVisible(false)
      onDismiss?.()
    }

    const getDefaultIcon = () => {
      switch (variant) {
        case 'destructive':
          return (
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )
        case 'success':
          return (
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )
        case 'warning':
          return (
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          )
        case 'info':
          return (
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )
        default:
          return null
      }
    }

    if (!isVisible) return null

    return (
      <Alert ref={ref} variant={variant} className={cn("relative", className)} {...props}>
        {(icon || variant !== 'default') && (icon || getDefaultIcon())}
        
        <div className="flex-1">
          {title && <AlertTitle>{title}</AlertTitle>}
          {description && <AlertDescription>{description}</AlertDescription>}
          {children}
        </div>

        <div className="flex items-center gap-2 ml-auto">
          {action}
          {dismissible && (
            <button
              onClick={handleDismiss}
              className="rounded-md p-1 hover:bg-black/5 dark:hover:bg-white/5 transition-colors"
              aria-label="Dismiss alert"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </Alert>
    )
  }
)

EnhancedAlert.displayName = "EnhancedAlert"

// Convenience components for common alert types
const SuccessAlert = React.forwardRef<HTMLDivElement, Omit<EnhancedAlertProps, 'variant'>>(
  (props, ref) => <EnhancedAlert ref={ref} variant="success" {...props} />
)
SuccessAlert.displayName = "SuccessAlert"

const ErrorAlert = React.forwardRef<HTMLDivElement, Omit<EnhancedAlertProps, 'variant'>>(
  (props, ref) => <EnhancedAlert ref={ref} variant="destructive" {...props} />
)
ErrorAlert.displayName = "ErrorAlert"

const WarningAlert = React.forwardRef<HTMLDivElement, Omit<EnhancedAlertProps, 'variant'>>(
  (props, ref) => <EnhancedAlert ref={ref} variant="warning" {...props} />
)
WarningAlert.displayName = "WarningAlert"

const InfoAlert = React.forwardRef<HTMLDivElement, Omit<EnhancedAlertProps, 'variant'>>(
  (props, ref) => <EnhancedAlert ref={ref} variant="info" {...props} />
)
InfoAlert.displayName = "InfoAlert"

// Banner Alert for full-width notifications
interface BannerAlertProps extends EnhancedAlertProps {
  position?: 'top' | 'bottom'
  sticky?: boolean
}

const BannerAlert = React.forwardRef<HTMLDivElement, BannerAlertProps>(
  ({ className, position = 'top', sticky = false, ...props }, ref) => (
    <EnhancedAlert
      ref={ref}
      className={cn(
        "rounded-none border-x-0",
        sticky && "sticky z-50",
        position === 'top' ? "top-0 border-t-0" : "bottom-0 border-b-0",
        className
      )}
      {...props}
    />
  )
)
BannerAlert.displayName = "BannerAlert"

export {
  Alert,
  AlertTitle,
  AlertDescription,
  EnhancedAlert,
  SuccessAlert,
  ErrorAlert,
  WarningAlert,
  InfoAlert,
  BannerAlert,
  alertVariants,
}
