// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Makes all properties of T optional recursively
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/**
 * Makes specific properties of T required
 */
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

/**
 * Makes specific properties of T optional
 */
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

/**
 * Extracts the type of array elements
 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never

/**
 * Creates a type with only the specified keys from T
 */
export type PickByType<T, U> = {
  [K in keyof T as T[K] extends U ? K : never]: T[K]
}

/**
 * Creates a type excluding the specified keys from T
 */
export type OmitByType<T, U> = {
  [K in keyof T as T[K] extends U ? never : K]: T[K]
}

/**
 * Creates a union type of all values in an object
 */
export type ValueOf<T> = T[keyof T]

/**
 * Creates a type that represents a non-empty array
 */
export type NonEmptyArray<T> = [T, ...T[]]

/**
 * Creates a type for string literal unions
 */
export type StringLiteral<T> = T extends string ? (string extends T ? never : T) : never

/**
 * Creates a type for numeric literal unions
 */
export type NumericLiteral<T> = T extends number ? (number extends T ? never : T) : never

/**
 * Creates a type that excludes null and undefined
 */
export type NonNullable<T> = T extends null | undefined ? never : T

/**
 * Creates a type for function parameters
 */
export type Parameters<T extends (...args: any) => any> = T extends (...args: infer P) => any ? P : never

/**
 * Creates a type for function return type
 */
export type ReturnType<T extends (...args: any) => any> = T extends (...args: any) => infer R ? R : any

// ============================================================================
// FORM AND VALIDATION TYPES
// ============================================================================

/**
 * Form field state
 */
export interface FormField<T = string> {
  value: T
  error?: string
  touched: boolean
  dirty: boolean
}

/**
 * Form validation result
 */
export interface ValidationResult {
  isValid: boolean
  errors: Record<string, string>
}

/**
 * Loading state with optional error
 */
export interface LoadingState {
  loading: boolean
  error?: string | null
}

/**
 * Async operation state
 */
export interface AsyncState<T = any> extends LoadingState {
  data?: T | null
  success?: boolean
}

/**
 * Pagination state
 */
export interface PaginationState {
  page: number
  limit: number
  total: number
  hasNext: boolean
  hasPrev: boolean
}

/**
 * Sort state
 */
export interface SortState<T = string> {
  field: T
  direction: 'asc' | 'desc'
}

/**
 * Filter state
 */
export interface FilterState<T = Record<string, any>> {
  filters: T
  activeCount: number
}

// ============================================================================
// EVENT AND HANDLER TYPES
// ============================================================================

/**
 * Generic event handler
 */
export type EventHandler<T = Event> = (event: T) => void

/**
 * Change event handler for form inputs
 */
export type ChangeHandler<T = HTMLInputElement> = (event: React.ChangeEvent<T>) => void

/**
 * Click event handler
 */
export type ClickHandler<T = HTMLElement> = (event: React.MouseEvent<T>) => void

/**
 * Submit event handler
 */
export type SubmitHandler<T = HTMLFormElement> = (event: React.FormEvent<T>) => void

/**
 * Async event handler
 */
export type AsyncEventHandler<T = Event> = (event: T) => Promise<void>

// ============================================================================
// COMPONENT PROP TYPES
// ============================================================================

/**
 * Base component props
 */
export interface BaseComponentProps {
  className?: string
  id?: string
  'data-testid'?: string
}

/**
 * Component with children
 */
export interface WithChildren {
  children: React.ReactNode
}

/**
 * Component with optional children
 */
export interface WithOptionalChildren {
  children?: React.ReactNode
}

/**
 * Component with loading state
 */
export interface WithLoading {
  loading?: boolean
}

/**
 * Component with error state
 */
export interface WithError {
  error?: string | null
}

/**
 * Component with disabled state
 */
export interface WithDisabled {
  disabled?: boolean
}

/**
 * Component with variant
 */
export interface WithVariant<T extends string = string> {
  variant?: T
}

/**
 * Component with size
 */
export interface WithSize<T extends string = 'sm' | 'md' | 'lg'> {
  size?: T
}

// ============================================================================
// API AND DATA TYPES
// ============================================================================

/**
 * Generic API response wrapper
 */
export interface ApiResponseWrapper<T = any> {
  data: T
  status: number
  message?: string
  timestamp: string
}

/**
 * Error response
 */
export interface ErrorResponse {
  error: string
  code?: string | number
  details?: Record<string, any>
  timestamp: string
}

/**
 * Success response
 */
export interface SuccessResponse<T = any> {
  success: true
  data: T
  message?: string
}

/**
 * Failed response
 */
export interface FailedResponse {
  success: false
  error: string
  code?: string | number
}

/**
 * Union type for API responses
 */
export type ApiResult<T = any> = SuccessResponse<T> | FailedResponse

/**
 * Metadata for lists
 */
export interface ListMetadata {
  total: number
  page: number
  limit: number
  pages: number
}

/**
 * Paginated response
 */
export interface PaginatedResponse<T = any> {
  items: T[]
  metadata: ListMetadata
}
