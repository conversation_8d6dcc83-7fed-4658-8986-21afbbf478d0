import { NextRequest, NextResponse } from 'next/server'
import { UserProfile, UserProfileResponse } from '@/types/user'
import { validateUsername } from '@/lib/utils'

// Mock database - in a real app, this would be your database
const mockUsers: Record<string, UserProfile> = {
  'demo': {
    id: '1',
    username: 'demo',
    displayName: 'Demo User',
    bio: 'Welcome to my AvencaLink! Check out all my links below.',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    coverImage: 'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?w=400&h=200&fit=crop',
    isActive: true,
    isVerified: true,
    links: [
      {
        id: '1',
        title: 'My Website',
        url: 'https://example.com',
        description: 'Check out my personal website',
        isActive: true,
        order: 1,
        icon: '🌐',
        clickCount: 42
      },
      {
        id: '2',
        title: 'Instagram',
        url: 'https://instagram.com/demo',
        description: 'Follow me on Instagram',
        isActive: true,
        order: 2,
        icon: '📱',
        clickCount: 128
      },
      {
        id: '3',
        title: 'YouTube Channel',
        url: 'https://youtube.com/@demo',
        description: 'Subscribe to my YouTube channel',
        isActive: true,
        order: 3,
        icon: '🎥',
        clickCount: 89
      },
      {
        id: '4',
        title: 'Spotify Playlist',
        url: 'https://open.spotify.com/playlist/demo',
        description: 'Listen to my favorite songs',
        isActive: true,
        order: 4,
        icon: '🎵',
        clickCount: 67
      }
    ],
    socialLinks: {
      instagram: 'demo',
      twitter: 'demo',
      youtube: 'https://youtube.com/@demo',
      github: 'demo'
    },
    theme: {
      backgroundColor: '#ffffff',
      textColor: '#000000',
      linkColor: '#f3f4f6',
      buttonStyle: 'rounded'
    },
    analytics: {
      totalViews: 1234,
      totalClicks: 326,
      lastUpdated: new Date().toISOString()
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: new Date().toISOString()
  },
  'johndoe': {
    id: '2',
    username: 'johndoe',
    displayName: 'John Doe',
    bio: 'Software developer and content creator. Building amazing things with code!',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    isActive: true,
    isVerified: false,
    links: [
      {
        id: '5',
        title: 'GitHub',
        url: 'https://github.com/johndoe',
        description: 'Check out my code',
        isActive: true,
        order: 1,
        icon: '💻',
        clickCount: 234
      },
      {
        id: '6',
        title: 'LinkedIn',
        url: 'https://linkedin.com/in/johndoe',
        description: 'Connect with me professionally',
        isActive: true,
        order: 2,
        icon: '💼',
        clickCount: 156
      },
      {
        id: '7',
        title: 'Blog',
        url: 'https://johndoe.dev',
        description: 'Read my latest articles',
        isActive: true,
        order: 3,
        icon: '📝',
        clickCount: 89
      }
    ],
    socialLinks: {
      github: 'johndoe',
      linkedin: 'johndoe',
      twitter: 'johndoe'
    },
    theme: {
      backgroundColor: '#1f2937',
      textColor: '#ffffff',
      linkColor: '#374151',
      buttonStyle: 'pill'
    },
    analytics: {
      totalViews: 567,
      totalClicks: 479,
      lastUpdated: new Date().toISOString()
    },
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: new Date().toISOString()
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> }
): Promise<NextResponse<UserProfileResponse>> {
  try {
    const { username } = await params

    // Validate username format
    if (!validateUsername(username)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid username format',
        },
        { status: 400 }
      )
    }

    // Simulate database lookup delay
    await new Promise(resolve => setTimeout(resolve, 100))

    const user = mockUsers[username.toLowerCase()]

    if (!user || !user.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found',
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: user,
    })
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    )
  }
}
