"use client";

import { useState } from "react";
import { apiService } from "@/services/api";
import { UserProfile } from "@/types/user";

export default function TestFirebasePage() {
  const [username, setUsername] = useState("willcomotti");
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testFirebaseConnection = async () => {
    setLoading(true);
    setError(null);
    setProfile(null);

    try {
      console.log(`Testing Firebase connection for username: ${username}`);
      const result = await apiService.getUserProfile(username);
      setProfile(result);
      console.log("Firebase test successful:", result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
      console.error("Firebase test failed:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Firebase API Test</h1>

      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">
          Username to test:
        </label>
        <div className="flex gap-2">
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            className="border border-gray-300 rounded px-3 py-2 flex-1"
            placeholder="Enter username (e.g., willcomotti)"
          />
          <button
            onClick={testFirebaseConnection}
            disabled={loading || !username.trim()}
            className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
          >
            {loading ? "Testing..." : "Test Firebase"}
          </button>
        </div>
      </div>

      <div className="mb-4">
        <p className="text-sm text-gray-600">
          Firebase URL:{" "}
          {process.env.NEXT_PUBLIC_FIREBASE_API_BASE_URL ||
            "https://cardlink-bio-default-rtdb.firebaseio.com/users"}
          /{username}.json
        </p>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}

      {profile && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <h2 className="text-lg font-semibold mb-2">Profile Found!</h2>
          <div className="space-y-2">
            <p>
              <strong>Username:</strong> {profile.username}
            </p>
            <p>
              <strong>Display Name:</strong> {profile.displayName}
            </p>
            <p>
              <strong>Bio:</strong> {profile.bio}
            </p>
            <p>
              <strong>Active:</strong> {profile.isActive ? "Yes" : "No"}
            </p>
            <p>
              <strong>Links Count:</strong> {profile.links?.length || 0}
            </p>
            {profile.avatar && (
              <div>
                <strong>Avatar:</strong>
                <img
                  src={profile.avatar}
                  alt="Avatar"
                  className="w-16 h-16 rounded-full mt-2"
                />
              </div>
            )}
          </div>
        </div>
      )}

      {!loading && !error && !profile && (
        <div className="bg-gray-100 border border-gray-300 text-gray-700 px-4 py-3 rounded">
          Enter a username.
        </div>
      )}

      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">Environment Variables</h2>
        <div className="bg-gray-50 p-4 rounded">
          <p>
            <strong>NEXT_PUBLIC_FIREBASE_API_BASE_URL:</strong>{" "}
            {process.env.NEXT_PUBLIC_FIREBASE_API_BASE_URL ||
              "Not set (using default)"}
          </p>
          <p>
            <strong>NEXT_PUBLIC_API_URL:</strong>{" "}
            {process.env.NEXT_PUBLIC_API_URL || "Not set (using default)"}
          </p>
        </div>
      </div>
    </div>
  );
}
