import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="container mx-auto px-6 py-6">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary rounded-xl flex items-center justify-center shadow-md interactive">
              <span className="text-primary-foreground font-bold text-xl">
                A
              </span>
            </div>
            <span className="text-2xl font-bold text-foreground">
              AvencaLink
            </span>
          </div>

          <div className="flex items-center space-x-4">
            <ThemeToggle />
            <Button variant="ghost" asChild animation="hover">
              <Link href="/login">Sign In</Link>
            </Button>
            <Button asChild animation="lift">
              <Link href="/signup">Get Started</Link>
            </Button>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-6 py-20 text-center">
        <div className="max-w-5xl mx-auto space-y-8">
          <div className="animate-fade-in">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-foreground mb-6 leading-tight">
              One link to share
              <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                {" "}
                everything
              </span>
            </h1>
          </div>

          <div className="animate-slide-up">
            <p className="text-lg sm:text-xl lg:text-2xl text-muted-foreground mb-10 max-w-3xl mx-auto leading-relaxed text-balance">
              Create a beautiful landing page for all your links. Share your
              content, social media, and more with just one link.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16 animate-slide-up">
            <Button
              size="xl"
              asChild
              animation="lift"
              className="min-w-[200px]"
            >
              <Link href="/signup">Create Your AvencaLink</Link>
            </Button>
            <Button
              variant="outline"
              size="xl"
              asChild
              animation="hover"
              className="min-w-[200px]"
            >
              <Link href="/demo">View Demo Profile</Link>
            </Button>
          </div>

          {/* Demo Preview */}
          <div className="relative max-w-sm mx-auto animate-bounce-in">
            <div className="bg-card rounded-3xl shadow-strong p-8 border border-border/50 backdrop-blur-sm">
              <div className="text-center mb-8">
                <div className="w-20 h-20 bg-gradient-to-r from-primary to-primary/80 rounded-full mx-auto mb-6 shadow-lg interactive-lift"></div>
                <h3 className="font-semibold text-card-foreground text-lg mb-2">
                  @yourname
                </h3>
                <p className="text-sm text-muted-foreground">
                  Your bio goes here
                </p>
              </div>

              <div className="space-y-4">
                <div className="bg-muted/50 hover:bg-muted/80 rounded-xl p-4 text-sm transition-all duration-200 interactive cursor-pointer">
                  🌐 My Website
                </div>
                <div className="bg-muted/50 hover:bg-muted/80 rounded-xl p-4 text-sm transition-all duration-200 interactive cursor-pointer">
                  📱 Instagram
                </div>
                <div className="bg-muted/50 hover:bg-muted/80 rounded-xl p-4 text-sm transition-all duration-200 interactive cursor-pointer">
                  🎵 Spotify Playlist
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Features Section */}
      <section className="container mx-auto px-6 py-24">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Why choose AvencaLink?
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Everything you need to create a professional link-in-bio page with
            modern design and powerful features
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 lg:gap-12 max-w-6xl mx-auto">
          <div className="text-center group animate-slide-up">
            <div className="w-16 h-16 bg-primary/10 dark:bg-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-primary/20 dark:group-hover:bg-primary/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-8 h-8 text-primary"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-3 text-xl">
              Lightning Fast
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Built with Next.js for optimal performance and SEO
            </p>
          </div>

          <div
            className="text-center group animate-slide-up"
            style={{ animationDelay: "0.1s" }}
          >
            <div className="w-16 h-16 bg-success/10 dark:bg-success/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-success/20 dark:group-hover:bg-success/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-8 h-8 text-success"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-3 text-xl">
              Mobile First
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Responsive design that looks great on all devices
            </p>
          </div>

          <div
            className="text-center group animate-slide-up"
            style={{ animationDelay: "0.2s" }}
          >
            <div className="w-16 h-16 bg-warning/10 dark:bg-warning/20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-warning/20 dark:group-hover:bg-warning/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-8 h-8 text-warning"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-3 text-xl">
              Customizable
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              Personalize your page with themes and custom styling
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-border/50 py-12 mt-24">
        <div className="container mx-auto px-6 text-center">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center shadow-sm">
              <span className="text-primary-foreground font-bold text-sm">
                A
              </span>
            </div>
            <span className="text-lg font-semibold text-foreground">
              AvencaLink
            </span>
          </div>
          <p className="text-muted-foreground text-sm max-w-md mx-auto leading-relaxed">
            © 2024 AvencaLink. Built with Next.js and TypeScript.
            <br />
            Create beautiful link-in-bio pages in minutes.
          </p>
        </div>
      </footer>
    </div>
  );
}
