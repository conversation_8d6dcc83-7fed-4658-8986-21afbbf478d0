import { NextRequest, NextResponse } from 'next/server'
import { validateUsername } from '@/lib/utils'

// Mock analytics storage - in a real app, this would be your database
const mockAnalytics: Record<string, { views: number; lastViewed: string }> = {}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> }
): Promise<NextResponse> {
  try {
    const { username } = await params

    // Validate username format
    if (!validateUsername(username)) {
      return NextResponse.json(
        { success: false, error: 'Invalid username format' },
        { status: 400 }
      )
    }

    // Get client IP for basic duplicate prevention
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown'

    // In a real app, you'd implement more sophisticated duplicate prevention
    // For now, we'll just increment the view count
    
    if (!mockAnalytics[username]) {
      mockAnalytics[username] = { views: 0, lastViewed: '' }
    }

    mockAnalytics[username].views += 1
    mockAnalytics[username].lastViewed = new Date().toISOString()

    console.log(`Profile view tracked for ${username} from IP: ${clientIP}`)

    return NextResponse.json({
      success: true,
      message: 'View tracked successfully',
    })
  } catch (error) {
    console.error('Error tracking profile view:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
