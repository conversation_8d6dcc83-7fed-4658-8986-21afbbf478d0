// ============================================================================
// USER PROFILE UTILITY FUNCTIONS
// ============================================================================

import type { 
  UserProfile, 
  Link, 
  SectionItem, 
  Review, 
  GalleryImage,
  SocialMediaLink,
  FeaturesSection,
  ServicesSection,
  GenericSection
} from '@/types'

import {
  ICON_CLASSES,
  DEFAULT_COLOR_SCHEMES,
  PLACEHOLDER_IMAGES,
  createDefaultUserProfile,
  createDefaultLink,
  createDefaultSectionItem
} from '@/types'

// ============================================================================
// DATA TRANSFORMATION FUNCTIONS
// ============================================================================

/**
 * Transforms legacy user data to new format
 */
export function transformLegacyUserData(legacyData: any): UserProfile {
  // If data is already in new format, return as is
  if (legacyData.user && legacyData.settings && legacyData.links) {
    return legacyData as UserProfile
  }

  // Transform legacy format to new format
  const username = legacyData.username || 'unknown'
  const name = legacyData.displayName || legacyData.name || 'Unknown User'
  
  const profile = createDefaultUserProfile(username, name)
  
  // Map legacy fields
  if (legacyData.bio) profile.user.bio = legacyData.bio
  if (legacyData.avatar) profile.user.avatar = legacyData.avatar
  
  // Transform legacy links
  if (legacyData.links && Array.isArray(legacyData.links)) {
    profile.links = legacyData.links.map((link: any) => ({
      text: link.title || link.text || '',
      url: link.url || '#',
      classIcon: link.icon || link.classIcon || ICON_CLASSES.PLACEHOLDER
    }))
  }

  // Transform legacy theme
  if (legacyData.theme) {
    profile.settings.colors = {
      background: legacyData.theme.backgroundColor || DEFAULT_COLOR_SCHEMES.LIGHT.background,
      linkText: legacyData.theme.textColor || DEFAULT_COLOR_SCHEMES.LIGHT.linkText,
      primary: legacyData.theme.linkColor || DEFAULT_COLOR_SCHEMES.LIGHT.primary,
      secondary: DEFAULT_COLOR_SCHEMES.LIGHT.secondary,
      socialIconBackground: DEFAULT_COLOR_SCHEMES.LIGHT.socialIconBackground
    }
  }

  return profile
}

/**
 * Extracts enabled sections from a user profile
 */
export function getEnabledSections(profile: UserProfile): string[] {
  const enabledSections: string[] = []
  
  if (profile.featuresSection.enabled) enabledSections.push('features')
  if (profile.servicesSection.enabled) enabledSections.push('services')
  if (profile.genericSection.enabled) enabledSections.push('generic')
  if (profile.gallery.enabled) enabledSections.push('gallery')
  if (profile.reviews.enabled) enabledSections.push('reviews')
  if (profile.video.enabled) enabledSections.push('video')
  
  return enabledSections
}

/**
 * Gets all links from a user profile (main links + social media)
 */
export function getAllLinks(profile: UserProfile): Link[] {
  return [...profile.links, ...profile.socialMedia]
}

/**
 * Gets all section items from enabled sections
 */
export function getAllSectionItems(profile: UserProfile): SectionItem[] {
  const items: SectionItem[] = []
  
  if (profile.featuresSection.enabled) {
    items.push(...profile.featuresSection.items)
  }
  
  if (profile.servicesSection.enabled) {
    items.push(...profile.servicesSection.items)
  }
  
  if (profile.genericSection.enabled) {
    items.push(...profile.genericSection.items)
  }
  
  return items
}

/**
 * Counts total content items in a profile
 */
export function getContentStats(profile: UserProfile): {
  totalLinks: number
  totalSectionItems: number
  totalGalleryImages: number
  totalReviews: number
  enabledSections: number
} {
  return {
    totalLinks: profile.links.length + profile.socialMedia.length,
    totalSectionItems: getAllSectionItems(profile).length,
    totalGalleryImages: profile.gallery.enabled ? profile.gallery.images.length : 0,
    totalReviews: profile.reviews.enabled ? profile.reviews.reviews.length : 0,
    enabledSections: getEnabledSections(profile).length
  }
}

// ============================================================================
// SEARCH AND FILTER FUNCTIONS
// ============================================================================

/**
 * Searches for links by text or URL
 */
export function searchLinks(links: Link[], query: string): Link[] {
  const lowercaseQuery = query.toLowerCase()
  return links.filter(link => 
    link.text.toLowerCase().includes(lowercaseQuery) ||
    link.url.toLowerCase().includes(lowercaseQuery)
  )
}

/**
 * Filters links by icon type
 */
export function filterLinksByIcon(links: Link[], iconPattern: string): Link[] {
  return links.filter(link => link.classIcon.includes(iconPattern))
}

/**
 * Gets social media links only
 */
export function getSocialMediaLinks(profile: UserProfile): SocialMediaLink[] {
  return profile.socialMedia.filter(link => 
    Object.values(ICON_CLASSES).some(iconClass => 
      link.classIcon === iconClass && 
      ['instagram', 'facebook', 'youtube', 'tiktok', 'twitter', 'linkedin'].some(platform =>
        iconClass.includes(platform)
      )
    )
  )
}

/**
 * Gets contact links (phone, email, whatsapp)
 */
export function getContactLinks(profile: UserProfile): Link[] {
  const contactIcons = [ICON_CLASSES.WHATSAPP, ICON_CLASSES.PHONE, ICON_CLASSES.EMAIL]
  return getAllLinks(profile).filter(link => 
    contactIcons.includes(link.classIcon as any)
  )
}

// ============================================================================
// SORTING FUNCTIONS
// ============================================================================

/**
 * Sorts section items by ID
 */
export function sortSectionItemsById(items: SectionItem[]): SectionItem[] {
  return [...items].sort((a, b) => a.id - b.id)
}

/**
 * Sorts reviews by rating (highest first)
 */
export function sortReviewsByRating(reviews: Review[]): Review[] {
  return [...reviews].sort((a, b) => b.rating - a.rating)
}

/**
 * Sorts gallery images by ID
 */
export function sortGalleryImagesById(images: GalleryImage[]): GalleryImage[] {
  return [...images].sort((a, b) => a.id - b.id)
}

// ============================================================================
// URL AND LINK UTILITIES
// ============================================================================

/**
 * Extracts domain from URL
 */
export function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname.replace('www.', '')
  } catch {
    return 'unknown'
  }
}

/**
 * Checks if URL is a social media link
 */
export function isSocialMediaUrl(url: string): boolean {
  const socialDomains = [
    'instagram.com',
    'facebook.com', 
    'youtube.com',
    'tiktok.com',
    'twitter.com',
    'linkedin.com',
    'pinterest.com'
  ]
  
  const domain = extractDomain(url)
  return socialDomains.some(socialDomain => domain.includes(socialDomain))
}

/**
 * Checks if URL is a WhatsApp link
 */
export function isWhatsAppUrl(url: string): boolean {
  return url.includes('wa.me') || url.includes('whatsapp.com')
}

/**
 * Formats phone number for display
 */
export function formatPhoneNumber(phone: string): string {
  if (!phone) return ''
  
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '')
  
  // Format based on length
  if (digits.length === 11) {
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 7)}-${digits.slice(7)}`
  } else if (digits.length === 10) {
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 6)}-${digits.slice(6)}`
  }
  
  return phone // Return original if can't format
}

// ============================================================================
// COLOR AND THEME UTILITIES
// ============================================================================

/**
 * Gets contrast color (black or white) for a given background color
 */
export function getContrastColor(backgroundColor: string): string {
  // Remove # if present
  const hex = backgroundColor.replace('#', '')
  
  // Convert to RGB
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
  
  return luminance > 0.5 ? '#000000' : '#ffffff'
}

/**
 * Generates CSS custom properties from color settings
 */
export function generateCSSCustomProperties(colors: any): Record<string, string> {
  return {
    '--color-background': colors.background,
    '--color-link-text': colors.linkText,
    '--color-primary': colors.primary,
    '--color-secondary': colors.secondary,
    '--color-social-icon-bg': colors.socialIconBackground,
  }
}

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

/**
 * Checks if a profile has minimum required content
 */
export function hasMinimumContent(profile: UserProfile): boolean {
  return (
    profile.user.name.length > 0 &&
    profile.user.username.length > 0 &&
    (profile.links.length > 0 || profile.socialMedia.length > 0)
  )
}

/**
 * Gets missing required fields from a profile
 */
export function getMissingRequiredFields(profile: UserProfile): string[] {
  const missing: string[] = []
  
  if (!profile.user.name) missing.push('name')
  if (!profile.user.username) missing.push('username')
  if (profile.links.length === 0 && profile.socialMedia.length === 0) {
    missing.push('links')
  }
  
  return missing
}
