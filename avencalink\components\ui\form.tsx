import * as React from "react"
import { cn } from "@/lib/utils"

interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode
}

const Form = React.forwardRef<HTMLFormElement, FormProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <form
        ref={ref}
        className={cn("space-y-6", className)}
        {...props}
      >
        {children}
      </form>
    )
  }
)

Form.displayName = "Form"

interface FormFieldProps {
  children: React.ReactNode
  className?: string
}

const FormField = React.forwardRef<HTMLDivElement, FormFieldProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("space-y-2", className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)

FormField.displayName = "FormField"

interface FormLabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean
}

const FormLabel = React.forwardRef<HTMLLabelElement, FormLabelProps>(
  ({ className, children, required, ...props }, ref) => {
    return (
      <label
        ref={ref}
        className={cn(
          "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
          className
        )}
        {...props}
      >
        {children}
        {required && <span className="text-destructive ml-1">*</span>}
      </label>
    )
  }
)

FormLabel.displayName = "FormLabel"

interface FormDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}

const FormDescription = React.forwardRef<HTMLParagraphElement, FormDescriptionProps>(
  ({ className, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={cn("text-sm text-muted-foreground", className)}
        {...props}
      />
    )
  }
)

FormDescription.displayName = "FormDescription"

interface FormMessageProps extends React.HTMLAttributes<HTMLParagraphElement> {
  type?: 'error' | 'success' | 'warning' | 'info'
}

const FormMessage = React.forwardRef<HTMLParagraphElement, FormMessageProps>(
  ({ className, children, type = 'error', ...props }, ref) => {
    const getIcon = () => {
      switch (type) {
        case 'success':
          return (
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )
        case 'warning':
          return (
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          )
        case 'info':
          return (
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )
        default:
          return (
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )
      }
    }

    const getColorClass = () => {
      switch (type) {
        case 'success':
          return 'text-success'
        case 'warning':
          return 'text-warning'
        case 'info':
          return 'text-info'
        default:
          return 'text-destructive'
      }
    }

    if (!children) return null

    return (
      <p
        ref={ref}
        className={cn(
          "text-sm flex items-center gap-1",
          getColorClass(),
          className
        )}
        {...props}
      >
        {getIcon()}
        {children}
      </p>
    )
  }
)

FormMessage.displayName = "FormMessage"

interface FormGroupProps {
  children: React.ReactNode
  className?: string
  orientation?: 'vertical' | 'horizontal'
}

const FormGroup = React.forwardRef<HTMLDivElement, FormGroupProps>(
  ({ className, children, orientation = 'vertical', ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "space-y-4",
          orientation === 'horizontal' && "flex space-y-0 space-x-4",
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

FormGroup.displayName = "FormGroup"

interface FormActionsProps {
  children: React.ReactNode
  className?: string
  align?: 'left' | 'center' | 'right'
}

const FormActions = React.forwardRef<HTMLDivElement, FormActionsProps>(
  ({ className, children, align = 'right', ...props }, ref) => {
    const alignmentClass = {
      left: 'justify-start',
      center: 'justify-center',
      right: 'justify-end',
    }[align]

    return (
      <div
        ref={ref}
        className={cn(
          "flex items-center gap-3 pt-4",
          alignmentClass,
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

FormActions.displayName = "FormActions"

export {
  Form,
  FormField,
  FormLabel,
  FormDescription,
  FormMessage,
  FormGroup,
  FormActions,
}
