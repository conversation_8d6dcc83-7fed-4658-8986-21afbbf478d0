/* [next]/internal/font/google/geist_e531dabc.module.css [app-client] (css) */
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwYGFWNOITddY4-s.b7d310ad.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwSGFWNOITddY4-s.81df3a5b.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Fallback;
  src: local(Arial);
  ascent-override: 95.94%;
  descent-override: 28.16%;
  line-gap-override: 0.0%;
  size-adjust: 104.76%;
}

.geist_e531dabc-module__QGiZLq__className {
  font-family: Geist, Geist Fallback;
  font-style: normal;
}

.geist_e531dabc-module__QGiZLq__variable {
  --font-geist-sans: "Geist", "Geist Fallback";
}


/* [next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css) */
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrMdmhHkjkotbA-s.cb6bbcb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrkdmhHkjkotbA-s.e32db976.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkjko-s.p.be19f591.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Mono Fallback;
  src: local(Arial);
  ascent-override: 74.67%;
  descent-override: 21.92%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.geist_mono_68a01160-module__YLcDdW__className {
  font-family: Geist Mono, Geist Mono Fallback;
  font-style: normal;
}

.geist_mono_68a01160-module__YLcDdW__variable {
  --font-geist-mono: "Geist Mono", "Geist Mono Fallback";
}


/* [project]/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: var(--font-geist-sans), system-ui, sans-serif;
    --font-mono: var(--font-geist-mono), monospace;
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-300: oklch(80.8% .114 19.571);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-indigo-100: oklch(93% .034 272.788);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --container-6xl: 72rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --tracking-tight: -.025em;
    --tracking-wider: .05em;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    --radius-sm: .125rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --shadow-xs: 0 1px 2px 0 #0000000d;
    --shadow-sm: 0 1px 3px 0 #0000001a, 0 1px 2px -1px #0000001a;
    --shadow-md: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
    --shadow-lg: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
    --shadow-xl: 0 20px 25px -5px #0000001a, 0 8px 10px -6px #0000001a;
    --shadow-2xl: 0 25px 50px -12px #00000040;
    --drop-shadow-sm: 0 1px 2px #00000026;
    --drop-shadow-lg: 0 4px 4px #00000026;
    --ease-in: cubic-bezier(.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, .2, 1);
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-ping: ping 1s cubic-bezier(0, 0, .2, 1) infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-xs: 4px;
    --blur-sm: 8px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --shadow-inner: inset 0 2px 4px 0 #0000000d;
    --color-background: hsl(var(--background));
    --color-foreground: hsl(var(--foreground));
    --color-card: hsl(var(--card));
    --color-card-foreground: hsl(var(--card-foreground));
    --color-primary: hsl(var(--primary));
    --color-primary-foreground: hsl(var(--primary-foreground));
    --color-secondary: hsl(var(--secondary));
    --color-secondary-foreground: hsl(var(--secondary-foreground));
    --color-muted: hsl(var(--muted));
    --color-muted-foreground: hsl(var(--muted-foreground));
    --color-accent: hsl(var(--accent));
    --color-accent-foreground: hsl(var(--accent-foreground));
    --color-destructive: hsl(var(--destructive));
    --color-destructive-foreground: hsl(var(--destructive-foreground));
    --color-border: hsl(var(--border));
    --color-input: hsl(var(--input));
    --color-ring: hsl(var(--ring));
    --color-success: #16a249;
    --color-success-foreground: #f8f7f7;
    --color-warning: #f59f0a;
    --color-warning-foreground: #fef3c8;
    --color-info: #0da2e7;
    --color-info-foreground: #f8fafc;
    --space-0: 0;
    --space-1: .25rem;
    --space-2: .5rem;
    --space-3: .75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --radius-none: 0;
    --radius-full: 9999px;
    --leading-none: 1;
    --font-thin: 100;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    --font-black: 900;
    --shadow-soft: 0 2px 8px #0000001a;
    --shadow-medium: 0 4px 16px #00000026;
    --shadow-strong: 0 8px 32px #0003;
    --animate-fade-in: fadeIn .5s ease-in-out;
    --animate-slide-up: slideUp .3s ease-out;
    --animate-slide-down: slideDown .3s ease-out;
    --animate-slide-left: slideLeft .3s ease-out;
    --animate-slide-right: slideRight .3s ease-out;
    --animate-scale-in: scaleIn .2s ease-out;
    --animate-scale-out: scaleOut .2s ease-in;
    --animate-bounce-in: bounceIn .6s ease-out;
    --ease-linear: linear;
    --ease-spring: cubic-bezier(.175, .885, .32, 1.275);
    --ease-bounce: cubic-bezier(.68, -.55, .265, 1.55);
    --z-0: 0;
    --z-10: 10;
    --z-20: 20;
    --z-30: 30;
    --z-40: 40;
    --z-50: 50;
    --z-auto: auto;
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  *, :after, :before, ::backdrop, ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: .5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }

  * {
    border-color: var(--color-border);
  }

  body {
    background-color: var(--color-background);
    color: var(--color-foreground);
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components;

@layer utilities {
  .\@container\/card-header {
    container: card-header / inline-size;
  }

  .pointer-events-auto {
    pointer-events: auto;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-6 {
    top: calc(var(--spacing) * 6);
  }

  .top-\[50\%\] {
    top: 50%;
  }

  .-right-1 {
    right: calc(var(--spacing) * -1);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-6 {
    right: calc(var(--spacing) * 6);
  }

  .-bottom-1 {
    bottom: calc(var(--spacing) * -1);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-3 {
    left: calc(var(--spacing) * 3);
  }

  .left-4 {
    left: calc(var(--spacing) * 4);
  }

  .left-\[50\%\] {
    left: 50%;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-50 {
    z-index: 50;
  }

  .z-\[100\] {
    z-index: 100;
  }

  .col-start-2 {
    grid-column-start: 2;
  }

  .row-span-2 {
    grid-row: span 2 / span 2;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .mx-0 {
    margin-inline: calc(var(--spacing) * 0);
  }

  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mt-24 {
    margin-top: calc(var(--spacing) * 24);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .ml-0 {
    margin-left: calc(var(--spacing) * 0);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }

  .ml-auto {
    margin-left: auto;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .size-2\.5 {
    width: calc(var(--spacing) * 2.5);
    height: calc(var(--spacing) * 2.5);
  }

  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .size-10 {
    width: calc(var(--spacing) * 10);
    height: calc(var(--spacing) * 10);
  }

  .size-12 {
    width: calc(var(--spacing) * 12);
    height: calc(var(--spacing) * 12);
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-14 {
    height: calc(var(--spacing) * 14);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-24 {
    height: calc(var(--spacing) * 24);
  }

  .h-48 {
    height: calc(var(--spacing) * 48);
  }

  .h-56 {
    height: calc(var(--spacing) * 56);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-80 {
    height: calc(var(--spacing) * 80);
  }

  .h-96 {
    height: calc(var(--spacing) * 96);
  }

  .h-auto {
    height: auto;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .max-h-\[95vh\] {
    max-height: 95vh;
  }

  .max-h-screen {
    max-height: 100vh;
  }

  .min-h-16 {
    min-height: calc(var(--spacing) * 16);
  }

  .min-h-\[400px\] {
    min-height: 400px;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }

  .w-1\/2 {
    width: 50%;
  }

  .w-1\/3 {
    width: 33.3333%;
  }

  .w-1\/4 {
    width: 25%;
  }

  .w-1\/6 {
    width: 16.6667%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }

  .w-2\/3 {
    width: 66.6667%;
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-9 {
    width: calc(var(--spacing) * 9);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-14 {
    width: calc(var(--spacing) * 14);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-28 {
    width: calc(var(--spacing) * 28);
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-px {
    width: 1px;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-5xl {
    max-width: var(--container-5xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-16 {
    max-width: calc(var(--spacing) * 16);
  }

  .max-w-\[95vw\] {
    max-width: 95vw;
  }

  .max-w-\[280px\] {
    max-width: 280px;
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .max-w-xl {
    max-width: var(--container-xl);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-\[200px\] {
    min-width: 200px;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-\[0_0_90\%\] {
    flex: 0 0 90%;
  }

  .flex-\[0_0_100\%\] {
    flex: 0 0 100%;
  }

  .flex-shrink-0, .shrink-0 {
    flex-shrink: 0;
  }

  .origin-\(--radix-tooltip-content-transform-origin\) {
    transform-origin: var(--radix-tooltip-content-transform-origin);
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-4 {
    --tw-translate-y: calc(var(--spacing) * 4);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[calc\(-50\%-2px\)\] {
    --tw-translate-y: calc(-50% - 2px);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-0 {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-95 {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-\[0\.98\] {
    scale: .98;
  }

  .-rotate-90 {
    rotate: -90deg;
  }

  .rotate-45 {
    rotate: 45deg;
  }

  .-skew-x-12 {
    --tw-skew-x: skewX(calc(12deg * -1));
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-\[shimmer_2s_infinite\] {
    animation: 2s infinite shimmer;
  }

  .animate-ping {
    animation: var(--animate-ping);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-help {
    cursor: help;
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .auto-rows-min {
    grid-auto-rows: min-content;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-rows-\[auto_auto\] {
    grid-template-rows: auto auto;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  :where(.space-y-0 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(var(--space-0) * var(--tw-space-y-reverse));
    margin-block-end: calc(var(--space-0) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(var(--space-1) * var(--tw-space-y-reverse));
    margin-block-end: calc(var(--space-1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(var(--space-2) * var(--tw-space-y-reverse));
    margin-block-end: calc(var(--space-2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(var(--space-3) * var(--tw-space-y-reverse));
    margin-block-end: calc(var(--space-3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(var(--space-4) * var(--tw-space-y-reverse));
    margin-block-end: calc(var(--space-4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(var(--space-6) * var(--tw-space-y-reverse));
    margin-block-end: calc(var(--space-6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(var(--space-8) * var(--tw-space-y-reverse));
    margin-block-end: calc(var(--space-8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(var(--space-1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(var(--space-1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(var(--space-2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(var(--space-2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(var(--space-3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(var(--space-3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(var(--space-4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(var(--space-4) * calc(1 - var(--tw-space-x-reverse)));
  }

  .self-start {
    align-self: flex-start;
  }

  .justify-self-end {
    justify-self: flex-end;
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-\[2px\] {
    border-radius: 2px;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
    border-radius: var(--radius-full);
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-none {
    border-radius: 0;
    border-radius: var(--radius-none);
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }

  .border-x-0 {
    border-inline-style: var(--tw-border-style);
    border-inline-width: 0;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-t-0 {
    border-top-style: var(--tw-border-style);
    border-top-width: 0;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-0 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-dotted {
    --tw-border-style: dotted;
    border-style: dotted;
  }

  .border-background {
    border-color: var(--color-background);
  }

  .border-border {
    border-color: var(--color-border);
  }

  .border-border\/50 {
    border-color: color-mix(in srgb, hsl(var(--border)) 50%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-border\/50 {
      border-color: color-mix(in oklab, var(--color-border) 50%, transparent);
    }
  }

  .border-current {
    border-color: currentColor;
  }

  .border-destructive {
    border-color: var(--color-destructive);
  }

  .border-destructive\/20 {
    border-color: color-mix(in srgb, hsl(var(--destructive)) 20%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-destructive\/20 {
      border-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
    }
  }

  .border-destructive\/50 {
    border-color: color-mix(in srgb, hsl(var(--destructive)) 50%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-destructive\/50 {
      border-color: color-mix(in oklab, var(--color-destructive) 50%, transparent);
    }
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-green-400 {
    border-color: var(--color-green-400);
  }

  .border-info {
    border-color: var(--color-info);
  }

  .border-info\/50 {
    border-color: #0da2e780;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-info\/50 {
      border-color: color-mix(in oklab, var(--color-info) 50%, transparent);
    }
  }

  .border-input {
    border-color: var(--color-input);
  }

  .border-muted {
    border-color: var(--color-muted);
  }

  .border-muted-foreground {
    border-color: var(--color-muted-foreground);
  }

  .border-muted-foreground\/25 {
    border-color: color-mix(in srgb, hsl(var(--muted-foreground)) 25%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-muted-foreground\/25 {
      border-color: color-mix(in oklab, var(--color-muted-foreground) 25%, transparent);
    }
  }

  .border-primary {
    border-color: var(--color-primary);
  }

  .border-primary\/20 {
    border-color: color-mix(in srgb, hsl(var(--primary)) 20%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-primary\/20 {
      border-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
    }
  }

  .border-red-400 {
    border-color: var(--color-red-400);
  }

  .border-success {
    border-color: var(--color-success);
  }

  .border-success\/50 {
    border-color: #16a24980;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-success\/50 {
      border-color: color-mix(in oklab, var(--color-success) 50%, transparent);
    }
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-warning {
    border-color: var(--color-warning);
  }

  .border-warning\/50 {
    border-color: #f59f0a80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-warning\/50 {
      border-color: color-mix(in oklab, var(--color-warning) 50%, transparent);
    }
  }

  .border-t-primary {
    border-top-color: var(--color-primary);
  }

  .border-t-transparent {
    border-top-color: #0000;
  }

  .bg-background {
    background-color: var(--color-background);
  }

  .bg-background\/20 {
    background-color: color-mix(in srgb, hsl(var(--background)) 20%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/20 {
      background-color: color-mix(in oklab, var(--color-background) 20%, transparent);
    }
  }

  .bg-background\/80 {
    background-color: color-mix(in srgb, hsl(var(--background)) 80%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/80 {
      background-color: color-mix(in oklab, var(--color-background) 80%, transparent);
    }
  }

  .bg-black\/50 {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-border {
    background-color: var(--color-border);
  }

  .bg-card {
    background-color: var(--color-card);
  }

  .bg-card\/20 {
    background-color: color-mix(in srgb, hsl(var(--card)) 20%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-card\/20 {
      background-color: color-mix(in oklab, var(--color-card) 20%, transparent);
    }
  }

  .bg-destructive {
    background-color: var(--color-destructive);
  }

  .bg-destructive\/5 {
    background-color: color-mix(in srgb, hsl(var(--destructive)) 5%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-destructive\/5 {
      background-color: color-mix(in oklab, var(--color-destructive) 5%, transparent);
    }
  }

  .bg-destructive\/10 {
    background-color: color-mix(in srgb, hsl(var(--destructive)) 10%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-destructive\/10 {
      background-color: color-mix(in oklab, var(--color-destructive) 10%, transparent);
    }
  }

  .bg-destructive\/20 {
    background-color: color-mix(in srgb, hsl(var(--destructive)) 20%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-destructive\/20 {
      background-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
    }
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-400 {
    background-color: var(--color-green-400);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-info\/10 {
    background-color: #0da2e71a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-info\/10 {
      background-color: color-mix(in oklab, var(--color-info) 10%, transparent);
    }
  }

  .bg-info\/20 {
    background-color: #0da2e733;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-info\/20 {
      background-color: color-mix(in oklab, var(--color-info) 20%, transparent);
    }
  }

  .bg-muted {
    background-color: var(--color-muted);
  }

  .bg-muted-foreground {
    background-color: var(--color-muted-foreground);
  }

  .bg-muted-foreground\/25 {
    background-color: color-mix(in srgb, hsl(var(--muted-foreground)) 25%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted-foreground\/25 {
      background-color: color-mix(in oklab, var(--color-muted-foreground) 25%, transparent);
    }
  }

  .bg-muted\/50 {
    background-color: color-mix(in srgb, hsl(var(--muted)) 50%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/50 {
      background-color: color-mix(in oklab, var(--color-muted) 50%, transparent);
    }
  }

  .bg-primary {
    background-color: var(--color-primary);
  }

  .bg-primary\/10 {
    background-color: color-mix(in srgb, hsl(var(--primary)) 10%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/10 {
      background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);
    }
  }

  .bg-primary\/20 {
    background-color: color-mix(in srgb, hsl(var(--primary)) 20%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/20 {
      background-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
    }
  }

  .bg-primary\/90 {
    background-color: color-mix(in srgb, hsl(var(--primary)) 90%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/90 {
      background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);
    }
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-secondary {
    background-color: var(--color-secondary);
  }

  .bg-success\/10 {
    background-color: #16a2491a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-success\/10 {
      background-color: color-mix(in oklab, var(--color-success) 10%, transparent);
    }
  }

  .bg-success\/20 {
    background-color: #16a24933;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-success\/20 {
      background-color: color-mix(in oklab, var(--color-success) 20%, transparent);
    }
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-warning\/10 {
    background-color: #f59f0a1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-warning\/10 {
      background-color: color-mix(in oklab, var(--color-warning) 10%, transparent);
    }
  }

  .bg-warning\/20 {
    background-color: #f59f0a33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-warning\/20 {
      background-color: color-mix(in oklab, var(--color-warning) 20%, transparent);
    }
  }

  .bg-white\/20 {
    background-color: #fff3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/20 {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .bg-linear-to-r {
    --tw-gradient-position: to right;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  @supports (background-image: linear-gradient(in lab, red, red)) {
    .bg-linear-to-r {
      --tw-gradient-position: to right in oklab;
    }
  }

  .bg-linear-to-t {
    --tw-gradient-position: to top;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  @supports (background-image: linear-gradient(in lab, red, red)) {
    .bg-linear-to-t {
      --tw-gradient-position: to top in oklab;
    }
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-black\/80 {
    --tw-gradient-from: #000c;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-black\/80 {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 80%, transparent);
    }
  }

  .from-black\/90 {
    --tw-gradient-from: #000000e6;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-black\/90 {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 90%, transparent);
    }
  }

  .from-blue-50 {
    --tw-gradient-from: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-muted {
    --tw-gradient-from: var(--color-muted);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-primary {
    --tw-gradient-from: var(--color-primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-primary\/10 {
    --tw-gradient-from: color-mix(in srgb, hsl(var(--primary)) 10%, transparent);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-primary\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-primary) 10%, transparent);
    }
  }

  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white\/5 {
    --tw-gradient-from: #ffffff0d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/5 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .from-white\/10 {
    --tw-gradient-from: #ffffff1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .via-black\/20 {
    --tw-gradient-via: #0003;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-black\/20 {
      --tw-gradient-via: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .via-black\/60 {
    --tw-gradient-via: #0009;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-black\/60 {
      --tw-gradient-via: color-mix(in oklab, var(--color-black) 60%, transparent);
    }
  }

  .via-muted\/50 {
    --tw-gradient-via: color-mix(in srgb, hsl(var(--muted)) 50%, transparent);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-muted\/50 {
      --tw-gradient-via: color-mix(in oklab, var(--color-muted) 50%, transparent);
    }
  }

  .via-primary\/20 {
    --tw-gradient-via: color-mix(in srgb, hsl(var(--primary)) 20%, transparent);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-primary\/20 {
      --tw-gradient-via: color-mix(in oklab, var(--color-primary) 20%, transparent);
    }
  }

  .via-white\/10 {
    --tw-gradient-via: #ffffff1a;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-white\/10 {
      --tw-gradient-via: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .via-white\/20 {
    --tw-gradient-via: #fff3;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-white\/20 {
      --tw-gradient-via: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .via-white\/30 {
    --tw-gradient-via: #ffffff4d;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-white\/30 {
      --tw-gradient-via: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }

  .to-indigo-100 {
    --tw-gradient-to: var(--color-indigo-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-muted {
    --tw-gradient-to: var(--color-muted);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-primary\/10 {
    --tw-gradient-to: color-mix(in srgb, hsl(var(--primary)) 10%, transparent);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-primary\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-primary) 10%, transparent);
    }
  }

  .to-primary\/70 {
    --tw-gradient-to: color-mix(in srgb, hsl(var(--primary)) 70%, transparent);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-primary\/70 {
      --tw-gradient-to: color-mix(in oklab, var(--color-primary) 70%, transparent);
    }
  }

  .to-primary\/80 {
    --tw-gradient-to: color-mix(in srgb, hsl(var(--primary)) 80%, transparent);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-primary\/80 {
      --tw-gradient-to: color-mix(in oklab, var(--color-primary) 80%, transparent);
    }
  }

  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-white\/5 {
    --tw-gradient-to: #ffffff0d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-white\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .to-white\/10 {
    --tw-gradient-to: #ffffff1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-white\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .bg-\[length\:200\%_100\%\] {
    background-size: 200% 100%;
  }

  .bg-clip-text {
    background-clip: text;
  }

  .fill-primary {
    fill: var(--color-primary);
  }

  .object-cover {
    object-fit: cover;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .py-24 {
    padding-block: calc(var(--spacing) * 24);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }

  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }

  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }

  .pl-32 {
    padding-left: calc(var(--spacing) * 32);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .font-bold {
    font-family: var(--font-bold);
  }

  .font-medium {
    font-family: var(--font-medium);
  }

  .font-mono {
    font-family: var(--font-mono);
  }

  .font-semibold {
    font-family: var(--font-semibold);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .leading-none {
    --tw-leading: var(--leading-none);
    line-height: 1;
    line-height: var(--leading-none);
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }

  .break-words {
    overflow-wrap: break-word;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-card-foreground {
    color: var(--color-card-foreground);
  }

  .text-destructive {
    color: var(--color-destructive);
  }

  .text-destructive-foreground {
    color: var(--color-destructive-foreground);
  }

  .text-foreground {
    color: var(--color-foreground);
  }

  .text-foreground\/50 {
    color: color-mix(in srgb, hsl(var(--foreground)) 50%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-foreground\/50 {
      color: color-mix(in oklab, var(--color-foreground) 50%, transparent);
    }
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-info-foreground {
    color: var(--color-info-foreground);
  }

  .text-muted-foreground {
    color: var(--color-muted-foreground);
  }

  .text-primary {
    color: var(--color-primary);
  }

  .text-primary-foreground {
    color: var(--color-primary-foreground);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-secondary-foreground {
    color: var(--color-secondary-foreground);
  }

  .text-success-foreground {
    color: var(--color-success-foreground);
  }

  .text-transparent {
    color: #0000;
  }

  .text-warning-foreground {
    color: var(--color-warning-foreground);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/90 {
    color: #ffffffe6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/90 {
      color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .text-yellow-400 {
    color: var(--color-yellow-400);
  }

  .lowercase {
    text-transform: lowercase;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .underline {
    text-decoration-line: underline;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-20 {
    opacity: .2;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-60 {
    opacity: .6;
  }

  .opacity-70 {
    opacity: .7;
  }

  .opacity-75 {
    opacity: .75;
  }

  .opacity-90 {
    opacity: .9;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-soft {
    --tw-shadow: 0 2px 8px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-strong {
    --tw-shadow: 0 8px 32px var(--tw-shadow-color, #0003);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-4 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-primary\/20 {
    --tw-ring-color: color-mix(in srgb, hsl(var(--primary)) 20%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-primary\/20 {
      --tw-ring-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
    }
  }

  .ring-ring {
    --tw-ring-color: var(--color-ring);
  }

  .ring-offset-2 {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .ring-offset-background {
    --tw-ring-offset-color: var(--color-background);
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-lg {
    --tw-drop-shadow-size: drop-shadow(0 4px 4px var(--tw-drop-shadow-color, #00000026));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-lg));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-sm {
    --tw-drop-shadow-size: drop-shadow(0 1px 2px var(--tw-drop-shadow-color, #00000026));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-sm));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-xs {
    --tw-backdrop-blur: blur(var(--blur-xs));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-75 {
    --tw-duration: 75ms;
    transition-duration: 75ms;
  }

  .duration-150 {
    --tw-duration: .15s;
    transition-duration: .15s;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .duration-700 {
    --tw-duration: .7s;
    transition-duration: .7s;
  }

  .ease-in {
    --tw-ease: var(--ease-in);
    transition-timing-function: var(--ease-in);
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }

  .ease-spring {
    --tw-ease: var(--ease-spring);
    transition-timing-function: var(--ease-spring);
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (hover: hover) {
    .group-hover\:translate-x-full:is(:where(.group):hover *) {
      --tw-translate-x: 100%;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:translate-y-0:is(:where(.group):hover *) {
      --tw-translate-y: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:translate-y-\[-2px\]:is(:where(.group):hover *) {
      --tw-translate-y: -2px;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-105:is(:where(.group):hover *) {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-110:is(:where(.group):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-primary\/20:is(:where(.group):hover *) {
      background-color: color-mix(in srgb, hsl(var(--primary)) 20%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-hover\:bg-primary\/20:is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-success\/20:is(:where(.group):hover *) {
      background-color: #16a24933;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-hover\:bg-success\/20:is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-success) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-warning\/20:is(:where(.group):hover *) {
      background-color: #f59f0a33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-hover\:bg-warning\/20:is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-warning) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-40:is(:where(.group):hover *) {
      opacity: .4;
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-80:is(:where(.group):hover *) {
      opacity: .8;
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\/btn\:scale-110:is(:where(.group\/btn):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  .group-active\:scale-95:is(:where(.group):active *) {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .group-active\:scale-100:is(:where(.group):active *) {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .group-active\:duration-75:is(:where(.group):active *) {
    --tw-duration: 75ms;
    transition-duration: 75ms;
  }

  .group-\[\.destructive\]\:border-muted\/40:is(:where(.group).destructive *) {
    border-color: color-mix(in srgb, hsl(var(--muted)) 40%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .group-\[\.destructive\]\:border-muted\/40:is(:where(.group).destructive *) {
      border-color: color-mix(in oklab, var(--color-muted) 40%, transparent);
    }
  }

  .group-\[\.destructive\]\:text-red-300:is(:where(.group).destructive *) {
    color: var(--color-red-300);
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-70:is(:where(.peer):disabled ~ *) {
    opacity: .7;
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:font-medium::file-selector-button {
    font-family: var(--font-medium);
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:text-foreground::file-selector-button {
    color: var(--color-foreground);
  }

  .placeholder\:text-muted-foreground::placeholder {
    color: var(--color-muted-foreground);
  }

  @media (hover: hover) {
    .hover\:-translate-y-0\.5:hover {
      --tw-translate-y: calc(var(--spacing) * -.5);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-110:hover {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-\[1\.01\]:hover {
      scale: 1.01;
    }
  }

  @media (hover: hover) {
    .hover\:scale-\[1\.02\]:hover {
      scale: 1.02;
    }
  }

  @media (hover: hover) {
    .hover\:border-border:hover {
      border-color: var(--color-border);
    }
  }

  @media (hover: hover) {
    .hover\:border-primary\/40:hover {
      border-color: color-mix(in srgb, hsl(var(--primary)) 40%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-primary\/40:hover {
        border-color: color-mix(in oklab, var(--color-primary) 40%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-ring\/50:hover {
      border-color: color-mix(in srgb, hsl(var(--ring)) 50%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-ring\/50:hover {
        border-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: var(--color-accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-background:hover {
      background-color: var(--color-background);
    }
  }

  @media (hover: hover) {
    .hover\:bg-black\/5:hover {
      background-color: #0000000d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-black\/5:hover {
        background-color: color-mix(in oklab, var(--color-black) 5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/90:hover {
      background-color: color-mix(in srgb, hsl(var(--destructive)) 90%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--color-destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-100:hover {
      background-color: var(--color-green-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-info\/90:hover {
      background-color: #0da2e7e6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-info\/90:hover {
        background-color: color-mix(in oklab, var(--color-info) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/80:hover {
      background-color: color-mix(in srgb, hsl(var(--muted)) 80%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/80:hover {
        background-color: color-mix(in oklab, var(--color-muted) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary:hover {
      background-color: var(--color-primary);
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/5:hover {
      background-color: color-mix(in srgb, hsl(var(--primary)) 5%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/5:hover {
        background-color: color-mix(in oklab, var(--color-primary) 5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: color-mix(in srgb, hsl(var(--primary)) 90%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary:hover {
      background-color: var(--color-secondary);
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary\/80:hover {
      background-color: color-mix(in srgb, hsl(var(--secondary)) 80%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--color-secondary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-success\/90:hover {
      background-color: #16a249e6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-success\/90:hover {
        background-color: color-mix(in oklab, var(--color-success) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-warning\/90:hover {
      background-color: #f59f0ae6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-warning\/90:hover {
        background-color: color-mix(in oklab, var(--color-warning) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gradient-to-r:hover {
      --tw-gradient-position: to right in oklab;
      background-image: linear-gradient(var(--tw-gradient-stops));
    }
  }

  @media (hover: hover) {
    .hover\:from-primary\/90:hover {
      --tw-gradient-from: color-mix(in srgb, hsl(var(--primary)) 90%, transparent);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:from-primary\/90:hover {
        --tw-gradient-from: color-mix(in oklab, var(--color-primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:to-primary\/60:hover {
      --tw-gradient-to: color-mix(in srgb, hsl(var(--primary)) 60%, transparent);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:to-primary\/60:hover {
        --tw-gradient-to: color-mix(in oklab, var(--color-primary) 60%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:to-primary\/70:hover {
      --tw-gradient-to: color-mix(in srgb, hsl(var(--primary)) 70%, transparent);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:to-primary\/70:hover {
        --tw-gradient-to: color-mix(in oklab, var(--color-primary) 70%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-accent-foreground:hover {
      color: var(--color-accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-500:hover {
      color: var(--color-blue-500);
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground:hover {
      color: var(--color-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-700:hover {
      color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary:hover {
      color: var(--color-primary);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary-foreground:hover {
      color: var(--color-primary-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary\/80:hover {
      color: color-mix(in srgb, hsl(var(--primary)) 80%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-primary\/80:hover {
        color: color-mix(in oklab, var(--color-primary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-secondary-foreground:hover {
      color: var(--color-secondary-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow-2xl:hover {
      --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-md:hover {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-medium:hover {
      --tw-shadow: 0 4px 16px var(--tw-shadow-color, #00000026);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-strong:hover {
      --tw-shadow: 0 8px 32px var(--tw-shadow-color, #0003);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:ring-8:hover {
      --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(8px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-primary\/25:hover {
      --tw-shadow-color: color-mix(in srgb, hsl(var(--primary)) 25%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:shadow-primary\/25:hover {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-primary) 25%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:ring-primary\/40:hover {
      --tw-ring-color: color-mix(in srgb, hsl(var(--primary)) 40%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:ring-primary\/40:hover {
        --tw-ring-color: color-mix(in oklab, var(--color-primary) 40%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-\[\.destructive\]\:hover\:border-destructive\/30:is(:where(.group).destructive *):hover {
      border-color: color-mix(in srgb, hsl(var(--destructive)) 30%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-\[\.destructive\]\:hover\:border-destructive\/30:is(:where(.group).destructive *):hover {
        border-color: color-mix(in oklab, var(--color-destructive) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-\[\.destructive\]\:hover\:bg-destructive:is(:where(.group).destructive *):hover {
      background-color: var(--color-destructive);
    }
  }

  @media (hover: hover) {
    .group-\[\.destructive\]\:hover\:text-destructive-foreground:is(:where(.group).destructive *):hover {
      color: var(--color-destructive-foreground);
    }
  }

  @media (hover: hover) {
    .group-\[\.destructive\]\:hover\:text-red-50:is(:where(.group).destructive *):hover {
      color: var(--color-red-50);
    }
  }

  .focus\:opacity-100:focus {
    opacity: 1;
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: var(--color-blue-500);
  }

  .focus\:ring-primary:focus {
    --tw-ring-color: var(--color-primary);
  }

  .focus\:ring-ring:focus {
    --tw-ring-color: var(--color-ring);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus\:outline-hidden:focus {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .group-\[\.destructive\]\:focus\:ring-destructive:is(:where(.group).destructive *):focus {
    --tw-ring-color: var(--color-destructive);
  }

  .group-\[\.destructive\]\:focus\:ring-red-400:is(:where(.group).destructive *):focus {
    --tw-ring-color: var(--color-red-400);
  }

  .group-\[\.destructive\]\:focus\:ring-offset-red-600:is(:where(.group).destructive *):focus {
    --tw-ring-offset-color: var(--color-red-600);
  }

  .focus-visible\:border-ring:focus-visible {
    border-color: var(--color-ring);
  }

  .focus-visible\:ring-0:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-2:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-destructive:focus-visible {
    --tw-ring-color: var(--color-destructive);
  }

  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: color-mix(in srgb, hsl(var(--destructive)) 20%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-destructive\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
    }
  }

  .focus-visible\:ring-info:focus-visible {
    --tw-ring-color: var(--color-info);
  }

  .focus-visible\:ring-primary:focus-visible {
    --tw-ring-color: var(--color-primary);
  }

  .focus-visible\:ring-ring:focus-visible {
    --tw-ring-color: var(--color-ring);
  }

  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: color-mix(in srgb, hsl(var(--ring)) 50%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
    }
  }

  .focus-visible\:ring-success:focus-visible {
    --tw-ring-color: var(--color-success);
  }

  .focus-visible\:ring-warning:focus-visible {
    --tw-ring-color: var(--color-warning);
  }

  .focus-visible\:ring-offset-0:focus-visible {
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:ring-offset-2:focus-visible {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:ring-offset-background:focus-visible {
    --tw-ring-offset-color: var(--color-background);
  }

  .focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  .active\:translate-y-0:active {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .active\:scale-95:active {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .active\:scale-\[0\.98\]:active {
    scale: .98;
  }

  .active\:bg-accent\/80:active {
    background-color: color-mix(in srgb, hsl(var(--accent)) 80%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .active\:bg-accent\/80:active {
      background-color: color-mix(in oklab, var(--color-accent) 80%, transparent);
    }
  }

  .active\:bg-destructive\/95:active {
    background-color: color-mix(in srgb, hsl(var(--destructive)) 95%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .active\:bg-destructive\/95:active {
      background-color: color-mix(in oklab, var(--color-destructive) 95%, transparent);
    }
  }

  .active\:bg-info\/95:active {
    background-color: #0da2e7f2;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .active\:bg-info\/95:active {
      background-color: color-mix(in oklab, var(--color-info) 95%, transparent);
    }
  }

  .active\:bg-primary\/95:active {
    background-color: color-mix(in srgb, hsl(var(--primary)) 95%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .active\:bg-primary\/95:active {
      background-color: color-mix(in oklab, var(--color-primary) 95%, transparent);
    }
  }

  .active\:bg-secondary\/90:active {
    background-color: color-mix(in srgb, hsl(var(--secondary)) 90%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .active\:bg-secondary\/90:active {
      background-color: color-mix(in oklab, var(--color-secondary) 90%, transparent);
    }
  }

  .active\:bg-success\/95:active {
    background-color: #16a249f2;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .active\:bg-success\/95:active {
      background-color: color-mix(in oklab, var(--color-success) 95%, transparent);
    }
  }

  .active\:bg-warning\/95:active {
    background-color: #f59f0af2;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .active\:bg-warning\/95:active {
      background-color: color-mix(in oklab, var(--color-warning) 95%, transparent);
    }
  }

  .active\:from-primary\/95:active {
    --tw-gradient-from: color-mix(in srgb, hsl(var(--primary)) 95%, transparent);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .active\:from-primary\/95:active {
      --tw-gradient-from: color-mix(in oklab, var(--color-primary) 95%, transparent);
    }
  }

  .active\:to-primary\/75:active {
    --tw-gradient-to: color-mix(in srgb, hsl(var(--primary)) 75%, transparent);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .active\:to-primary\/75:active {
      --tw-gradient-to: color-mix(in oklab, var(--color-primary) 75%, transparent);
    }
  }

  .active\:shadow-sm:active {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-30:disabled {
    opacity: .3;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  @media (hover: hover) {
    .disabled\:hover\:scale-100:disabled:hover {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
    grid-template-columns: 1fr auto;
  }

  .aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: var(--color-destructive);
  }

  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: color-mix(in srgb, hsl(var(--destructive)) 20%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
    }
  }

  .data-\[active\=true\]\:border-b-2[data-active="true"] {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .data-\[active\=true\]\:border-primary[data-active="true"] {
    border-color: var(--color-primary);
  }

  .data-\[active\=true\]\:bg-accent[data-active="true"] {
    background-color: var(--color-accent);
  }

  .data-\[active\=true\]\:bg-background[data-active="true"] {
    background-color: var(--color-background);
  }

  .data-\[active\=true\]\:text-accent-foreground[data-active="true"] {
    color: var(--color-accent-foreground);
  }

  .data-\[active\=true\]\:text-foreground[data-active="true"] {
    color: var(--color-foreground);
  }

  .data-\[active\=true\]\:shadow-sm[data-active="true"] {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[orientation\=horizontal\]\:h-px[data-orientation="horizontal"] {
    height: 1px;
  }

  .data-\[orientation\=horizontal\]\:w-full[data-orientation="horizontal"] {
    width: 100%;
  }

  .data-\[orientation\=vertical\]\:h-full[data-orientation="vertical"] {
    height: 100%;
  }

  .data-\[orientation\=vertical\]\:w-px[data-orientation="vertical"] {
    width: 1px;
  }

  .data-\[state\=closed\]\:animate-scale-out[data-state="closed"] {
    animation: var(--animate-scale-out);
  }

  .data-\[state\=open\]\:animate-fade-in[data-state="open"] {
    animation: var(--animate-fade-in);
  }

  .data-\[state\=open\]\:animate-scale-in[data-state="open"] {
    animation: var(--animate-scale-in);
  }

  .data-\[state\=open\]\:bg-accent[data-state="open"] {
    background-color: var(--color-accent);
  }

  .data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
    color: var(--color-muted-foreground);
  }

  .data-\[swipe\=cancel\]\:translate-x-0[data-swipe="cancel"] {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"] {
    --tw-translate-x: var(--radix-toast-swipe-end-x);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"] {
    --tw-translate-x: var(--radix-toast-swipe-move-x);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[swipe\=move\]\:transition-none[data-swipe="move"] {
    transition-property: none;
  }

  @media (width >= 40rem) {
    .sm\:top-auto {
      top: auto;
    }
  }

  @media (width >= 40rem) {
    .sm\:right-0 {
      right: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 40rem) {
    .sm\:bottom-0 {
      bottom: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 40rem) {
    .sm\:mt-8 {
      margin-top: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-5 {
      height: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-7 {
      height: calc(var(--spacing) * 7);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-12 {
      height: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-14 {
      height: calc(var(--spacing) * 14);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-16 {
      height: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-28 {
      height: calc(var(--spacing) * 28);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-80 {
      height: calc(var(--spacing) * 80);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-96 {
      height: calc(var(--spacing) * 96);
    }
  }

  @media (width >= 40rem) {
    .sm\:min-h-18 {
      min-height: calc(var(--spacing) * 18);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-5 {
      width: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-7 {
      width: calc(var(--spacing) * 7);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-12 {
      width: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-14 {
      width: calc(var(--spacing) * 14);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-28 {
      width: calc(var(--spacing) * 28);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-\[0_0_85\%\] {
      flex: 0 0 85%;
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-col {
      flex-direction: column;
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-4 {
      gap: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-y-4 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(var(--space-4) * var(--tw-space-y-reverse));
      margin-block-end: calc(var(--space-4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-x-2 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(var(--space-2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(var(--space-2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-x-3 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(var(--space-3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(var(--space-3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-x-4 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(var(--space-4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(var(--space-4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (width >= 40rem) {
    .sm\:rounded-2xl {
      border-radius: var(--radius-2xl);
    }
  }

  @media (width >= 40rem) {
    .sm\:rounded-lg {
      border-radius: var(--radius-lg);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-6 {
      padding: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-8 {
      padding: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-2 {
      padding-inline: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-3 {
      padding-block: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-left {
      text-align: left;
    }
  }

  @media (width >= 40rem) {
    .sm\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:leading-loose {
      --tw-leading: var(--leading-loose);
      line-height: var(--leading-loose);
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-\[420px\] {
      max-width: 420px;
    }
  }

  @media (width >= 48rem) {
    .md\:flex-\[0_0_80\%\] {
      flex: 0 0 80%;
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:mb-12 {
      margin-bottom: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 64rem) {
    .lg\:h-6 {
      height: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:h-18 {
      height: calc(var(--spacing) * 18);
    }
  }

  @media (width >= 64rem) {
    .lg\:h-32 {
      height: calc(var(--spacing) * 32);
    }
  }

  @media (width >= 64rem) {
    .lg\:h-96 {
      height: calc(var(--spacing) * 96);
    }
  }

  @media (width >= 64rem) {
    .lg\:min-h-20 {
      min-height: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 64rem) {
    .lg\:w-6 {
      width: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:w-32 {
      width: calc(var(--spacing) * 32);
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-2xl {
      max-width: var(--container-2xl);
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-xl {
      max-width: var(--container-xl);
    }
  }

  @media (width >= 64rem) {
    .lg\:flex-\[0_0_360px\] {
      flex: 0 0 360px;
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-12 {
      gap: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 64rem) {
    .lg\:p-8 {
      padding: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-4 {
      padding-inline: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 64rem) {
    .lg\:py-4 {
      padding-block: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 64rem) {
    .lg\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (width >= 80rem) {
    .xl\:text-7xl {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }

  .dark\:border-destructive:is(.dark *) {
    border-color: var(--color-destructive);
  }

  .dark\:border-gray-700:is(.dark *) {
    border-color: var(--color-gray-700);
  }

  .dark\:border-info:is(.dark *) {
    border-color: var(--color-info);
  }

  .dark\:border-success:is(.dark *) {
    border-color: var(--color-success);
  }

  .dark\:border-warning:is(.dark *) {
    border-color: var(--color-warning);
  }

  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: color-mix(in srgb, hsl(var(--destructive)) 60%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-destructive) 60%, transparent);
    }
  }

  .dark\:bg-gray-900:is(.dark *) {
    background-color: var(--color-gray-900);
  }

  .dark\:bg-primary\/20:is(.dark *) {
    background-color: color-mix(in srgb, hsl(var(--primary)) 20%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-primary\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
    }
  }

  .dark\:bg-success\/20:is(.dark *) {
    background-color: #16a24933;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-success\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-success) 20%, transparent);
    }
  }

  .dark\:bg-warning\/20:is(.dark *) {
    background-color: #f59f0a33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-warning\/20:is(.dark *) {
      background-color: color-mix(in oklab, var(--color-warning) 20%, transparent);
    }
  }

  .dark\:from-gray-900:is(.dark *) {
    --tw-gradient-from: var(--color-gray-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-gray-800:is(.dark *) {
    --tw-gradient-to: var(--color-gray-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:text-gray-100:is(.dark *) {
    color: var(--color-gray-100);
  }

  .dark\:text-gray-300:is(.dark *) {
    color: var(--color-gray-300);
  }

  .dark\:text-gray-400:is(.dark *) {
    color: var(--color-gray-400);
  }

  .dark\:text-white:is(.dark *) {
    color: var(--color-white);
  }

  @media (hover: hover) {
    .dark\:group-hover\:bg-primary\/30:is(.dark *):is(:where(.group):hover *) {
      background-color: color-mix(in srgb, hsl(var(--primary)) 30%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:group-hover\:bg-primary\/30:is(.dark *):is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-primary) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:group-hover\:bg-success\/30:is(.dark *):is(:where(.group):hover *) {
      background-color: #16a2494d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:group-hover\:bg-success\/30:is(.dark *):is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-success) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:group-hover\:bg-warning\/30:is(.dark *):is(:where(.group):hover *) {
      background-color: #f59f0a4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:group-hover\:bg-warning\/30:is(.dark *):is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-warning) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-white\/5:is(.dark *):hover {
      background-color: #ffffff0d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-white\/5:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-gray-300:is(.dark *):hover {
      color: var(--color-gray-300);
    }
  }

  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: color-mix(in srgb, hsl(var(--destructive)) 40%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
    }
  }

  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: color-mix(in srgb, hsl(var(--destructive)) 40%, transparent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
    }
  }

  .\[\&_p\]\:leading-relaxed p {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\.border-b\]\:pb-6.border-b {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .\[\.border-t\]\:pt-6.border-t {
    padding-top: calc(var(--spacing) * 6);
  }

  .\[\&\>svg\]\:pointer-events-none > svg {
    pointer-events: none;
  }

  .\[\&\>svg\]\:absolute > svg {
    position: absolute;
  }

  .\[\&\>svg\]\:top-4 > svg {
    top: calc(var(--spacing) * 4);
  }

  .\[\&\>svg\]\:left-4 > svg {
    left: calc(var(--spacing) * 4);
  }

  .\[\&\>svg\]\:size-3 > svg {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:text-destructive > svg {
    color: var(--color-destructive);
  }

  .\[\&\>svg\]\:text-foreground > svg {
    color: var(--color-foreground);
  }

  .\[\&\>svg\]\:text-info > svg {
    color: var(--color-info);
  }

  .\[\&\>svg\]\:text-success > svg {
    color: var(--color-success);
  }

  .\[\&\>svg\]\:text-warning > svg {
    color: var(--color-warning);
  }

  .\[\&\>svg\+div\]\:translate-y-\[-3px\] > svg + div {
    --tw-translate-y: -3px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[\&\>svg\~\*\]\:pl-7 > svg ~ * {
    padding-left: calc(var(--spacing) * 7);
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-accent:hover {
      background-color: var(--color-accent);
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-destructive\/90:hover {
      background-color: color-mix(in srgb, hsl(var(--destructive)) 90%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--color-destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-primary\/90:hover {
      background-color: color-mix(in srgb, hsl(var(--primary)) 90%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-secondary\/90:hover {
      background-color: color-mix(in srgb, hsl(var(--secondary)) 90%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-secondary\/90:hover {
        background-color: color-mix(in oklab, var(--color-secondary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:text-accent-foreground:hover {
      color: var(--color-accent-foreground);
    }
  }

  .shadow-xs {
    box-shadow: var(--shadow-xs);
  }

  .shadow-sm {
    box-shadow: var(--shadow-sm);
  }

  .shadow-md {
    box-shadow: var(--shadow-md);
  }

  .shadow-lg {
    box-shadow: var(--shadow-lg);
  }

  .shadow-xl {
    box-shadow: var(--shadow-xl);
  }

  .shadow-2xl {
    box-shadow: var(--shadow-2xl);
  }

  .shadow-inner {
    box-shadow: var(--shadow-inner);
  }

  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }

  .shadow-strong {
    box-shadow: var(--shadow-strong);
  }

  .ease-linear {
    transition-timing-function: var(--ease-linear);
  }

  .ease-in {
    transition-timing-function: var(--ease-in);
  }

  .ease-out {
    transition-timing-function: var(--ease-out);
  }

  .ease-in-out {
    transition-timing-function: var(--ease-in-out);
  }

  .ease-spring {
    transition-timing-function: var(--ease-spring);
  }

  .ease-bounce {
    transition-timing-function: var(--ease-bounce);
  }

  .animate-fade-in {
    animation: var(--animate-fade-in);
  }

  .animate-slide-up {
    animation: var(--animate-slide-up);
  }

  .animate-slide-down {
    animation: var(--animate-slide-down);
  }

  .animate-slide-left {
    animation: var(--animate-slide-left);
  }

  .animate-slide-right {
    animation: var(--animate-slide-right);
  }

  .animate-scale-in {
    animation: var(--animate-scale-in);
  }

  .animate-scale-out {
    animation: var(--animate-scale-out);
  }

  .animate-bounce-in {
    animation: var(--animate-bounce-in);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .space-0 {
    margin: var(--space-0);
  }

  .space-1 {
    margin: var(--space-1);
  }

  .space-2 {
    margin: var(--space-2);
  }

  .space-3 {
    margin: var(--space-3);
  }

  .space-4 {
    margin: var(--space-4);
  }

  .space-5 {
    margin: var(--space-5);
  }

  .space-6 {
    margin: var(--space-6);
  }

  .space-8 {
    margin: var(--space-8);
  }

  .space-10 {
    margin: var(--space-10);
  }

  .space-12 {
    margin: var(--space-12);
  }

  .space-16 {
    margin: var(--space-16);
  }

  .space-20 {
    margin: var(--space-20);
  }

  .space-24 {
    margin: var(--space-24);
  }

  .transform-gpu {
    transform: translateZ(0);
  }

  .line-clamp-1 {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-3 {
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-4 {
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-5 {
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-6 {
    -webkit-line-clamp: 6;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .focus-ring:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-ring-color: var(--color-primary);
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-offset-color: var(--color-background);
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-ring-inset:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-ring-color: var(--color-primary);
    --tw-outline-style: none;
    --tw-ring-inset: inset;
    outline-style: none;
  }

  .focus-visible-ring:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-ring-color: var(--color-primary);
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-offset-color: var(--color-background);
    --tw-outline-style: none;
    outline-style: none;
  }

  .btn-hover-lift {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  @media (hover: hover) {
    .btn-hover-lift:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .btn-hover-lift:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .btn-hover-glow {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  @media (hover: hover) {
    .btn-hover-glow:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .btn-hover-glow:hover {
      --tw-shadow-color: color-mix(in srgb, hsl(var(--primary)) 25%, transparent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .btn-hover-glow:hover {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-primary) 25%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  .interactive {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: .2s;
    --tw-ease: var(--ease-out);
    transition-duration: .2s;
    transition-timing-function: var(--ease-out);
  }

  @media (hover: hover) {
    .interactive:hover {
      scale: 1.02;
    }
  }

  .interactive:active {
    scale: .98;
  }

  .interactive-lift {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  @media (hover: hover) {
    .interactive-lift:hover {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .interactive-lift:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .glass {
    -webkit-backdrop-filter: blur(16px);
    background: #ffffff1a;
    border: 1px solid #fff3;
  }

  .glass-light {
    -webkit-backdrop-filter: blur(8px);
    background: #ffffff0d;
    border: 1px solid #ffffff1a;
  }

  .glass-dark {
    -webkit-backdrop-filter: blur(16px);
    background: #0000001a;
    border: 1px solid #ffffff1a;
  }

  .smooth-scroll {
    scroll-behavior: smooth;
  }

  .embla {
    overflow: hidden;
  }

  .embla__container {
    display: flex;
  }

  .embla__slide {
    flex: none;
    min-width: 0;
  }

  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  .container-narrow {
    max-width: 768px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .container-wide {
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .text-success {
    color: hsl(var(--color-success));
  }

  .text-warning {
    color: hsl(var(--color-warning));
  }

  .text-info {
    color: hsl(var(--color-info));
  }

  .bg-success {
    background-color: hsl(var(--color-success));
  }

  .bg-warning {
    background-color: hsl(var(--color-warning));
  }

  .bg-info {
    background-color: hsl(var(--color-info));
  }
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes ping {
  75%, 100% {
    opacity: 0;
    transform: scale(2);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .5;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  0% {
    opacity: 0;
    transform: translateX(10px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  0% {
    opacity: 0;
    transform: translateX(-10px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(.95);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  100% {
    opacity: 0;
    transform: scale(.95);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(.3);
  }

  50% {
    transform: scale(1.05);
  }

  70% {
    transform: scale(.9);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__9d126714._.css.map*/