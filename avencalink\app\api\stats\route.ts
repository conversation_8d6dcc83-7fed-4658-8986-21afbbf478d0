import { NextResponse } from 'next/server'

export async function GET(): Promise<NextResponse> {
  try {
    // Mock statistics - in a real app, this would query your database
    const stats = {
      totalUsers: 1247,
      totalLinks: 8934,
      totalClicks: 45672,
    }

    return NextResponse.json({
      success: true,
      data: stats,
    })
  } catch (error) {
    console.error('Error fetching stats:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
