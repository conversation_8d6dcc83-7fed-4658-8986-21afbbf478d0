"use client";

import * as React from "react";
import Image, { ImageProps } from "next/image";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";

interface OptimizedImageProps extends Omit<ImageProps, 'onLoad' | 'onError'> {
  fallbackSrc?: string;
  showSkeleton?: boolean;
  skeletonClassName?: string;
  containerClassName?: string;
  errorFallback?: React.ReactNode;
  onLoadComplete?: () => void;
  onError?: (error: Error) => void;
  lazy?: boolean;
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape' | number;
}

export function OptimizedImage({
  src,
  alt,
  fallbackSrc,
  showSkeleton = true,
  skeletonClassName,
  containerClassName,
  errorFallback,
  onLoadComplete,
  onError,
  lazy = true,
  aspectRatio,
  className,
  ...props
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = React.useState(true);
  const [hasError, setHasError] = React.useState(false);
  const [currentSrc, setCurrentSrc] = React.useState(src);
  const [isInView, setIsInView] = React.useState(!lazy);
  
  const imgRef = React.useRef<HTMLImageElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  React.useEffect(() => {
    if (!lazy || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isInView]);

  // Handle image load
  const handleLoad = React.useCallback(() => {
    setIsLoading(false);
    setHasError(false);
    onLoadComplete?.();
  }, [onLoadComplete]);

  // Handle image error
  const handleError = React.useCallback(() => {
    setIsLoading(false);
    setHasError(true);
    
    // Try fallback image if available
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc);
      setIsLoading(true);
      setHasError(false);
      return;
    }
    
    onError?.(new Error(`Failed to load image: ${currentSrc}`));
  }, [currentSrc, fallbackSrc, onError]);

  // Reset state when src changes
  React.useEffect(() => {
    setCurrentSrc(src);
    setIsLoading(true);
    setHasError(false);
  }, [src]);

  // Get aspect ratio styles
  const getAspectRatioStyles = () => {
    if (!aspectRatio) return {};
    
    if (typeof aspectRatio === 'number') {
      return { aspectRatio: aspectRatio.toString() };
    }
    
    const ratios = {
      square: '1 / 1',
      video: '16 / 9',
      portrait: '3 / 4',
      landscape: '4 / 3',
    };
    
    return { aspectRatio: ratios[aspectRatio] };
  };

  // Default error fallback
  const defaultErrorFallback = (
    <div className="flex items-center justify-center bg-muted text-muted-foreground">
      <svg
        className="h-8 w-8"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
        />
      </svg>
    </div>
  );

  return (
    <div
      ref={containerRef}
      className={cn("relative overflow-hidden", containerClassName)}
      style={getAspectRatioStyles()}
    >
      {/* Loading skeleton */}
      {isLoading && showSkeleton && (
        <Skeleton
          className={cn(
            "absolute inset-0 w-full h-full",
            skeletonClassName
          )}
        />
      )}

      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 w-full h-full">
          {errorFallback || defaultErrorFallback}
        </div>
      )}

      {/* Image */}
      {isInView && !hasError && (
        <Image
          ref={imgRef}
          src={currentSrc}
          alt={alt}
          className={cn(
            "transition-opacity duration-300",
            isLoading ? "opacity-0" : "opacity-100",
            className
          )}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />
      )}
    </div>
  );
}

// Avatar component with optimized image loading
interface OptimizedAvatarProps {
  src?: string;
  alt: string;
  fallback?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  className?: string;
  showOnlineIndicator?: boolean;
  isOnline?: boolean;
}

export function OptimizedAvatar({
  src,
  alt,
  fallback,
  size = 'md',
  className,
  showOnlineIndicator = false,
  isOnline = false,
}: OptimizedAvatarProps) {
  const sizeClasses = {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16',
    '2xl': 'w-20 h-20',
  };

  const indicatorSizes = {
    xs: 'w-1.5 h-1.5',
    sm: 'w-2 h-2',
    md: 'w-2.5 h-2.5',
    lg: 'w-3 h-3',
    xl: 'w-3.5 h-3.5',
    '2xl': 'w-4 h-4',
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={cn("relative inline-block", className)}>
      <div
        className={cn(
          "relative rounded-full overflow-hidden bg-muted",
          sizeClasses[size]
        )}
      >
        {src ? (
          <OptimizedImage
            src={src}
            alt={alt}
            fill
            className="object-cover"
            aspectRatio="square"
            errorFallback={
              <div className="flex items-center justify-center w-full h-full bg-primary text-primary-foreground text-sm font-medium">
                {fallback || getInitials(alt)}
              </div>
            }
          />
        ) : (
          <div className="flex items-center justify-center w-full h-full bg-primary text-primary-foreground text-sm font-medium">
            {fallback || getInitials(alt)}
          </div>
        )}
      </div>

      {/* Online indicator */}
      {showOnlineIndicator && (
        <div
          className={cn(
            "absolute bottom-0 right-0 rounded-full border-2 border-background",
            indicatorSizes[size],
            isOnline ? "bg-success" : "bg-muted-foreground"
          )}
        />
      )}
    </div>
  );
}

// Gallery component with optimized loading
interface OptimizedGalleryProps {
  images: Array<{
    src: string;
    alt: string;
    caption?: string;
  }>;
  columns?: number;
  gap?: number;
  className?: string;
  onImageClick?: (index: number) => void;
}

export function OptimizedGallery({
  images,
  columns = 3,
  gap = 4,
  className,
  onImageClick,
}: OptimizedGalleryProps) {
  return (
    <div
      className={cn("grid", className)}
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap: `${gap * 0.25}rem`,
      }}
    >
      {images.map((image, index) => (
        <div
          key={index}
          className="group cursor-pointer overflow-hidden rounded-lg"
          onClick={() => onImageClick?.(index)}
        >
          <OptimizedImage
            src={image.src}
            alt={image.alt}
            width={400}
            height={400}
            className="transition-transform duration-300 group-hover:scale-105"
            aspectRatio="square"
          />
          {image.caption && (
            <div className="p-2 text-sm text-muted-foreground">
              {image.caption}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
