import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import { MapPin, Navigation, Phone, Clock, ExternalLink } from "lucide-react";
import { useEffect, useState } from "react";
import "leaflet/dist/leaflet.css";
import L from "leaflet";

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as unknown as Record<string, unknown>)
  ._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
  iconUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
  shadowUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
});

interface LocationData {
  title: string;
  description: string;
  enabled: boolean;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  coordinates: {
    lat: number;
    lng: number;
  };
  contact: {
    phone: string;
    whatsapp: string;
  };
  hours: {
    weekdays: string;
    weekends: string;
    closed?: string;
  };
  googleMapsUrl: string;
}

interface LocationProps {
  locationData: LocationData;
}

const Location = ({ locationData }: LocationProps) => {
  const [isMapLoaded, setIsMapLoaded] = useState(false);

  useEffect(() => {
    // Ensure map loads properly
    const timer = setTimeout(() => {
      setIsMapLoaded(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  if (!locationData.enabled) return null;

  const handleDirections = () => {
    if (locationData.googleMapsUrl) {
      window.open(locationData.googleMapsUrl, "_blank", "noopener,noreferrer");
    }
  };

  const handleWhatsApp = () => {
    if (locationData.contact.whatsapp) {
      window.open(
        locationData.contact.whatsapp,
        "_blank",
        "noopener,noreferrer"
      );
    }
  };

  const handleCall = () => {
    if (locationData.contact.phone) {
      window.open(`tel:${locationData.contact.phone}`, "_self");
    }
  };

  return (
    <section className="mb-12 animate-fade-in">
      <div className="text-center mb-8 lg:mb-12">
        <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground mb-3">
          {locationData.title}
        </h2>
        <p className="text-muted-foreground max-w-md lg:max-w-2xl mx-auto text-sm sm:text-base leading-relaxed">
          {locationData.description}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Map Section */}
        <Card className="overflow-hidden shadow-soft hover:shadow-medium transition-all duration-300">
          <CardContent className="p-0">
            <div className="h-80 sm:h-96 relative">
              {isMapLoaded && (
                <MapContainer
                  center={[
                    locationData.coordinates.lat,
                    locationData.coordinates.lng,
                  ]}
                  zoom={15}
                  style={{ height: "100%", width: "100%" }}
                  className="rounded-lg"
                >
                  <TileLayer
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  />
                  <Marker
                    position={[
                      locationData.coordinates.lat,
                      locationData.coordinates.lng,
                    ]}
                  >
                    <Popup>
                      <div className="text-center p-2">
                        <h3 className="font-bold text-sm mb-1">
                          {locationData.address.street}
                        </h3>
                        <p className="text-xs text-muted-foreground">
                          {locationData.address.city},{" "}
                          {locationData.address.state}
                        </p>
                      </div>
                    </Popup>
                  </Marker>
                </MapContainer>
              )}
              {!isMapLoaded && (
                <div className="h-full w-full bg-muted flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Location Info Section */}
        <div className="space-y-6">
          {/* Address Card */}
          <Card className="shadow-soft hover:shadow-medium transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center shrink-0">
                  <MapPin className="w-5 h-5 text-primary" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-foreground mb-2">
                    Endereço
                  </h3>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p>{locationData.address.street}</p>
                    <p>
                      {locationData.address.city}, {locationData.address.state}
                    </p>
                    <p>
                      {locationData.address.zipCode} -{" "}
                      {locationData.address.country}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Card */}
          <Card className="shadow-soft hover:shadow-medium transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center shrink-0">
                  <Phone className="w-5 h-5 text-primary" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-foreground mb-2">
                    Contato
                  </h3>
                  <div className="text-sm text-muted-foreground space-y-2">
                    <p>{locationData.contact.phone}</p>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCall}
                        className="text-xs"
                      >
                        <Phone className="w-3 h-3 mr-1" />
                        Ligar
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleWhatsApp}
                        className="text-xs bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
                      >
                        <i className="fa fa-whatsapp mr-1" />
                        WhatsApp
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Hours Card */}
          <Card className="shadow-soft hover:shadow-medium transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center shrink-0">
                  <Clock className="w-5 h-5 text-primary" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-foreground mb-2">
                    Horário de Funcionamento
                  </h3>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <div className="flex justify-between">
                      <span>Segunda a Sexta:</span>
                      <span className="font-medium">
                        {locationData.hours.weekdays}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Fins de Semana:</span>
                      <span className="font-medium">
                        {locationData.hours.weekends}
                      </span>
                    </div>
                    {locationData.hours.closed && (
                      <div className="flex justify-between">
                        <span>Fechado:</span>
                        <span className="font-medium text-red-600">
                          {locationData.hours.closed}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Directions Button */}
          <Button
            onClick={handleDirections}
            className="w-full bg-primary hover:bg-primary/90 text-primary-foreground shadow-soft hover:shadow-medium transition-all duration-300 hover:scale-[1.02] transform-gpu"
            size="lg"
          >
            <Navigation className="w-4 h-4 mr-2" />
            Como Chegar
            <ExternalLink className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Location;
