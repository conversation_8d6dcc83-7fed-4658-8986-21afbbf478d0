{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css"], "sourcesContent": ["/* cyrillic */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwYGFWNOITddY4.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwSGFWNOITddY4.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwcGFWNOITd.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Geist Fallback';\n    src: local(\"Arial\");\n    ascent-override: 95.94%;\ndescent-override: 28.16%;\nline-gap-override: 0.00%;\nsize-adjust: 104.76%;\n\n}\n.className {\n    font-family: 'Geist', 'Geist Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-geist-sans: 'Geist', 'Geist Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css"], "sourcesContent": ["/* cyrillic */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrMdmhHkjkotbA.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrkdmhHkjkotbA.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrcdmhHkjko.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Geist Mono Fallback';\n    src: local(\"Arial\");\n    ascent-override: 74.67%;\ndescent-override: 21.92%;\nline-gap-override: 0.00%;\nsize-adjust: 134.59%;\n\n}\n.className {\n    font-family: 'Geist Mono', 'Geist Mono Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-geist-mono: 'Geist Mono', 'Geist Mono Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --font-sans: var(--font-geist-sans), system-ui, sans-serif;\n    --font-mono: var(--font-geist-mono), monospace;\n    --color-red-100: oklch(93.6% 0.032 17.717);\n    --color-red-400: oklch(70.4% 0.191 22.216);\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-red-700: oklch(50.5% 0.213 27.518);\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\n    --color-green-50: oklch(98.2% 0.018 155.826);\n    --color-green-100: oklch(96.2% 0.044 156.743);\n    --color-green-200: oklch(92.5% 0.084 155.995);\n    --color-green-400: oklch(79.2% 0.209 151.711);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-green-700: oklch(52.7% 0.154 150.069);\n    --color-green-900: oklch(39.3% 0.095 152.535);\n    --color-blue-50: oklch(97% 0.014 254.604);\n    --color-blue-100: oklch(93.2% 0.032 255.585);\n    --color-blue-500: oklch(62.3% 0.214 259.815);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-900: oklch(37.9% 0.146 265.522);\n    --color-indigo-100: oklch(93% 0.034 272.788);\n    --color-purple-100: oklch(94.6% 0.033 307.174);\n    --color-purple-600: oklch(55.8% 0.288 302.321);\n    --color-purple-900: oklch(38.1% 0.176 304.987);\n    --color-gray-50: oklch(98.5% 0.002 247.839);\n    --color-gray-100: oklch(96.7% 0.003 264.542);\n    --color-gray-200: oklch(92.8% 0.006 264.531);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-400: oklch(70.7% 0.022 261.325);\n    --color-gray-500: oklch(55.1% 0.027 264.364);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-zinc-200: oklch(92% 0.004 286.32);\n    --color-zinc-800: oklch(27.4% 0.006 286.033);\n    --color-zinc-950: oklch(14.1% 0.005 285.823);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-xs: 20rem;\n    --container-sm: 24rem;\n    --container-md: 28rem;\n    --container-lg: 32rem;\n    --container-xl: 36rem;\n    --container-2xl: 42rem;\n    --container-4xl: 56rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-5xl: 3rem;\n    --text-5xl--line-height: 1;\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --tracking-tight: -0.025em;\n    --leading-tight: 1.25;\n    --leading-relaxed: 1.625;\n    --leading-loose: 2;\n    --radius-md: calc(var(--radius) - 2px);\n    --radius-lg: var(--radius);\n    --radius-xl: 0.75rem;\n    --radius-2xl: 1rem;\n    --radius-3xl: 1.5rem;\n    --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);\n    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);\n    --ease-out: cubic-bezier(0, 0, 0.2, 1);\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-spin: spin 1s linear infinite;\n    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --blur-xs: 4px;\n    --blur-sm: 8px;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-sans);\n    --default-mono-font-family: var(--font-mono);\n    --color-background: hsl(var(--background));\n    --color-foreground: hsl(var(--foreground));\n    --color-card: hsl(var(--card));\n    --color-card-foreground: hsl(var(--card-foreground));\n    --color-primary: hsl(var(--primary));\n    --color-primary-foreground: hsl(var(--primary-foreground));\n    --color-secondary: hsl(var(--secondary));\n    --color-secondary-foreground: hsl(var(--secondary-foreground));\n    --color-muted: hsl(var(--muted));\n    --color-muted-foreground: hsl(var(--muted-foreground));\n    --color-accent: hsl(var(--accent));\n    --color-accent-foreground: hsl(var(--accent-foreground));\n    --color-destructive: hsl(var(--destructive));\n    --color-border: hsl(var(--border));\n    --color-input: hsl(var(--input));\n    --color-ring: hsl(var(--ring));\n    --animate-fade-in: fadeIn 0.5s ease-in-out;\n    --shadow-soft: 0 2px 8px rgba(0, 0, 0, 0.1);\n    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);\n    --shadow-strong: 0 8px 32px rgba(0, 0, 0, 0.2);\n    --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .\\@container\\/card-header {\n    container-type: inline-size;\n    container-name: card-header;\n  }\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border-width: 0;\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .static {\n    position: static;\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-1\\/2 {\n    top: calc(1/2 * 100%);\n  }\n  .top-4 {\n    top: calc(var(--spacing) * 4);\n  }\n  .top-6 {\n    top: calc(var(--spacing) * 6);\n  }\n  .-right-1 {\n    right: calc(var(--spacing) * -1);\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .right-4 {\n    right: calc(var(--spacing) * 4);\n  }\n  .right-6 {\n    right: calc(var(--spacing) * 6);\n  }\n  .-bottom-1 {\n    bottom: calc(var(--spacing) * -1);\n  }\n  .bottom-0 {\n    bottom: calc(var(--spacing) * 0);\n  }\n  .bottom-4 {\n    bottom: calc(var(--spacing) * 4);\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .left-1\\/2 {\n    left: calc(1/2 * 100%);\n  }\n  .left-3 {\n    left: calc(var(--spacing) * 3);\n  }\n  .left-4 {\n    left: calc(var(--spacing) * 4);\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-20 {\n    z-index: 20;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .col-start-2 {\n    grid-column-start: 2;\n  }\n  .row-span-2 {\n    grid-row: span 2 / span 2;\n  }\n  .row-start-1 {\n    grid-row-start: 1;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-4 {\n    margin-top: calc(var(--spacing) * 4);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-8 {\n    margin-top: calc(var(--spacing) * 8);\n  }\n  .mt-12 {\n    margin-top: calc(var(--spacing) * 12);\n  }\n  .mr-1 {\n    margin-right: calc(var(--spacing) * 1);\n  }\n  .mr-2 {\n    margin-right: calc(var(--spacing) * 2);\n  }\n  .mb-1 {\n    margin-bottom: calc(var(--spacing) * 1);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .mb-12 {\n    margin-bottom: calc(var(--spacing) * 12);\n  }\n  .ml-2 {\n    margin-left: calc(var(--spacing) * 2);\n  }\n  .line-clamp-3 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 3;\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline {\n    display: inline;\n  }\n  .inline-block {\n    display: inline-block;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .aspect-square {\n    aspect-ratio: 1 / 1;\n  }\n  .size-2\\.5 {\n    width: calc(var(--spacing) * 2.5);\n    height: calc(var(--spacing) * 2.5);\n  }\n  .size-8 {\n    width: calc(var(--spacing) * 8);\n    height: calc(var(--spacing) * 8);\n  }\n  .size-9 {\n    width: calc(var(--spacing) * 9);\n    height: calc(var(--spacing) * 9);\n  }\n  .size-full {\n    width: 100%;\n    height: 100%;\n  }\n  .h-1 {\n    height: calc(var(--spacing) * 1);\n  }\n  .h-1\\.5 {\n    height: calc(var(--spacing) * 1.5);\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-9 {\n    height: calc(var(--spacing) * 9);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-14 {\n    height: calc(var(--spacing) * 14);\n  }\n  .h-16 {\n    height: calc(var(--spacing) * 16);\n  }\n  .h-24 {\n    height: calc(var(--spacing) * 24);\n  }\n  .h-56 {\n    height: calc(var(--spacing) * 56);\n  }\n  .h-64 {\n    height: calc(var(--spacing) * 64);\n  }\n  .h-80 {\n    height: calc(var(--spacing) * 80);\n  }\n  .h-96 {\n    height: calc(var(--spacing) * 96);\n  }\n  .h-auto {\n    height: auto;\n  }\n  .h-full {\n    height: 100%;\n  }\n  .min-h-16 {\n    min-height: calc(var(--spacing) * 16);\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .w-1 {\n    width: calc(var(--spacing) * 1);\n  }\n  .w-1\\.5 {\n    width: calc(var(--spacing) * 1.5);\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-5 {\n    width: calc(var(--spacing) * 5);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-10 {\n    width: calc(var(--spacing) * 10);\n  }\n  .w-12 {\n    width: calc(var(--spacing) * 12);\n  }\n  .w-14 {\n    width: calc(var(--spacing) * 14);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-24 {\n    width: calc(var(--spacing) * 24);\n  }\n  .w-fit {\n    width: fit-content;\n  }\n  .w-full {\n    width: 100%;\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-4xl {\n    max-width: var(--container-4xl);\n  }\n  .max-w-16 {\n    max-width: calc(var(--spacing) * 16);\n  }\n  .max-w-\\[280px\\] {\n    max-width: 280px;\n  }\n  .max-w-full {\n    max-width: 100%;\n  }\n  .max-w-md {\n    max-width: var(--container-md);\n  }\n  .max-w-sm {\n    max-width: var(--container-sm);\n  }\n  .max-w-xl {\n    max-width: var(--container-xl);\n  }\n  .max-w-xs {\n    max-width: var(--container-xs);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .flex-\\[0_0_90\\%\\] {\n    flex: 0 0 90%;\n  }\n  .flex-\\[0_0_100\\%\\] {\n    flex: 0 0 100%;\n  }\n  .flex-shrink-0 {\n    flex-shrink: 0;\n  }\n  .shrink-0 {\n    flex-shrink: 0;\n  }\n  .origin-\\(--radix-tooltip-content-transform-origin\\) {\n    transform-origin: var(--radix-tooltip-content-transform-origin);\n  }\n  .-translate-x-1\\/2 {\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-full {\n    --tw-translate-x: -100%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-1\\/2 {\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-4 {\n    --tw-translate-y: calc(var(--spacing) * 4);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[calc\\(-50\\%-2px\\)\\] {\n    --tw-translate-y: calc(-50% - 2px);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .scale-0 {\n    --tw-scale-x: 0%;\n    --tw-scale-y: 0%;\n    --tw-scale-z: 0%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-95 {\n    --tw-scale-x: 95%;\n    --tw-scale-y: 95%;\n    --tw-scale-z: 95%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-100 {\n    --tw-scale-x: 100%;\n    --tw-scale-y: 100%;\n    --tw-scale-z: 100%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-\\[0\\.98\\] {\n    scale: 0.98;\n  }\n  .rotate-45 {\n    rotate: 45deg;\n  }\n  .-skew-x-12 {\n    --tw-skew-x: skewX(calc(12deg * -1));\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .transform {\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .transform-gpu {\n    transform: translateZ(0) var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .animate-fade-in {\n    animation: var(--animate-fade-in);\n  }\n  .animate-ping {\n    animation: var(--animate-ping);\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .animate-spin {\n    animation: var(--animate-spin);\n  }\n  .cursor-help {\n    cursor: help;\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .auto-rows-min {\n    grid-auto-rows: min-content;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-rows-\\[auto_auto\\] {\n    grid-template-rows: auto auto;\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-end {\n    align-items: flex-end;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .justify-start {\n    justify-content: flex-start;\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-1\\.5 {\n    gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-3 {\n    gap: calc(var(--spacing) * 3);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .gap-8 {\n    gap: calc(var(--spacing) * 8);\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-x-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .self-start {\n    align-self: flex-start;\n  }\n  .justify-self-end {\n    justify-self: flex-end;\n  }\n  .truncate {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-2xl {\n    border-radius: var(--radius-2xl);\n  }\n  .rounded-3xl {\n    border-radius: var(--radius-3xl);\n  }\n  .rounded-\\[2px\\] {\n    border-radius: 2px;\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius-lg);\n  }\n  .rounded-md {\n    border-radius: var(--radius-md);\n  }\n  .rounded-xl {\n    border-radius: var(--radius-xl);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-0 {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n  .border-2 {\n    border-style: var(--tw-border-style);\n    border-width: 2px;\n  }\n  .border-4 {\n    border-style: var(--tw-border-style);\n    border-width: 4px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-b-2 {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 2px;\n  }\n  .border-background {\n    border-color: var(--color-background);\n  }\n  .border-border\\/50 {\n    border-color: color-mix(in srgb, hsl(var(--border)) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-border) 50%, transparent);\n    }\n  }\n  .border-current {\n    border-color: currentcolor;\n  }\n  .border-gray-200 {\n    border-color: var(--color-gray-200);\n  }\n  .border-gray-300 {\n    border-color: var(--color-gray-300);\n  }\n  .border-green-200 {\n    border-color: var(--color-green-200);\n  }\n  .border-green-400 {\n    border-color: var(--color-green-400);\n  }\n  .border-primary {\n    border-color: var(--color-primary);\n  }\n  .border-primary\\/20 {\n    border-color: color-mix(in srgb, hsl(var(--primary)) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-primary) 20%, transparent);\n    }\n  }\n  .border-red-400 {\n    border-color: var(--color-red-400);\n  }\n  .border-transparent {\n    border-color: transparent;\n  }\n  .border-zinc-200 {\n    border-color: var(--color-zinc-200);\n  }\n  .border-zinc-800 {\n    border-color: var(--color-zinc-800);\n  }\n  .border-t-blue-600 {\n    border-top-color: var(--color-blue-600);\n  }\n  .border-t-transparent {\n    border-top-color: transparent;\n  }\n  .bg-background {\n    background-color: var(--color-background);\n  }\n  .bg-background\\/20 {\n    background-color: color-mix(in srgb, hsl(var(--background)) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-background) 20%, transparent);\n    }\n  }\n  .bg-background\\/80 {\n    background-color: color-mix(in srgb, hsl(var(--background)) 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-background) 80%, transparent);\n    }\n  }\n  .bg-black\\/50 {\n    background-color: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n  }\n  .bg-blue-100 {\n    background-color: var(--color-blue-100);\n  }\n  .bg-blue-500 {\n    background-color: var(--color-blue-500);\n  }\n  .bg-blue-600 {\n    background-color: var(--color-blue-600);\n  }\n  .bg-border {\n    background-color: var(--color-border);\n  }\n  .bg-card {\n    background-color: var(--color-card);\n  }\n  .bg-card\\/20 {\n    background-color: color-mix(in srgb, hsl(var(--card)) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-card) 20%, transparent);\n    }\n  }\n  .bg-destructive {\n    background-color: var(--color-destructive);\n  }\n  .bg-gray-50 {\n    background-color: var(--color-gray-50);\n  }\n  .bg-gray-100 {\n    background-color: var(--color-gray-100);\n  }\n  .bg-gray-200 {\n    background-color: var(--color-gray-200);\n  }\n  .bg-green-50 {\n    background-color: var(--color-green-50);\n  }\n  .bg-green-100 {\n    background-color: var(--color-green-100);\n  }\n  .bg-green-400 {\n    background-color: var(--color-green-400);\n  }\n  .bg-green-500 {\n    background-color: var(--color-green-500);\n  }\n  .bg-muted {\n    background-color: var(--color-muted);\n  }\n  .bg-muted\\/50 {\n    background-color: color-mix(in srgb, hsl(var(--muted)) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-muted) 50%, transparent);\n    }\n  }\n  .bg-primary {\n    background-color: var(--color-primary);\n  }\n  .bg-primary\\/10 {\n    background-color: color-mix(in srgb, hsl(var(--primary)) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);\n    }\n  }\n  .bg-primary\\/20 {\n    background-color: color-mix(in srgb, hsl(var(--primary)) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-primary) 20%, transparent);\n    }\n  }\n  .bg-primary\\/50 {\n    background-color: color-mix(in srgb, hsl(var(--primary)) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-primary) 50%, transparent);\n    }\n  }\n  .bg-primary\\/70 {\n    background-color: color-mix(in srgb, hsl(var(--primary)) 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-primary) 70%, transparent);\n    }\n  }\n  .bg-primary\\/80 {\n    background-color: color-mix(in srgb, hsl(var(--primary)) 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-primary) 80%, transparent);\n    }\n  }\n  .bg-primary\\/90 {\n    background-color: color-mix(in srgb, hsl(var(--primary)) 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);\n    }\n  }\n  .bg-purple-100 {\n    background-color: var(--color-purple-100);\n  }\n  .bg-red-100 {\n    background-color: var(--color-red-100);\n  }\n  .bg-secondary {\n    background-color: var(--color-secondary);\n  }\n  .bg-transparent {\n    background-color: transparent;\n  }\n  .bg-white {\n    background-color: var(--color-white);\n  }\n  .bg-white\\/20 {\n    background-color: color-mix(in srgb, #fff 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n    }\n  }\n  .bg-zinc-800 {\n    background-color: var(--color-zinc-800);\n  }\n  .bg-zinc-950 {\n    background-color: var(--color-zinc-950);\n  }\n  .bg-linear-to-br {\n    --tw-gradient-position: to bottom right;\n    @supports (background-image: linear-gradient(in lab, red, red)) {\n      --tw-gradient-position: to bottom right in oklab;\n    }\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-linear-to-r {\n    --tw-gradient-position: to right;\n    @supports (background-image: linear-gradient(in lab, red, red)) {\n      --tw-gradient-position: to right in oklab;\n    }\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-linear-to-t {\n    --tw-gradient-position: to top;\n    @supports (background-image: linear-gradient(in lab, red, red)) {\n      --tw-gradient-position: to top in oklab;\n    }\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-br {\n    --tw-gradient-position: to bottom right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-r {\n    --tw-gradient-position: to right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-t {\n    --tw-gradient-position: to top in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .from-black\\/80 {\n    --tw-gradient-from: color-mix(in srgb, #000 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-black) 80%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-black\\/90 {\n    --tw-gradient-from: color-mix(in srgb, #000 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-black) 90%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-50 {\n    --tw-gradient-from: var(--color-blue-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-500 {\n    --tw-gradient-from: var(--color-blue-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-primary {\n    --tw-gradient-from: var(--color-primary);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-primary\\/10 {\n    --tw-gradient-from: color-mix(in srgb, hsl(var(--primary)) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-primary) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-transparent {\n    --tw-gradient-from: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-white\\/5 {\n    --tw-gradient-from: color-mix(in srgb, #fff 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-white) 5%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-white\\/10 {\n    --tw-gradient-from: color-mix(in srgb, #fff 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-white) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .via-black\\/20 {\n    --tw-gradient-via: color-mix(in srgb, #000 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-black) 20%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-black\\/60 {\n    --tw-gradient-via: color-mix(in srgb, #000 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-black) 60%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-primary\\/20 {\n    --tw-gradient-via: color-mix(in srgb, hsl(var(--primary)) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-primary) 20%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-white\\/10 {\n    --tw-gradient-via: color-mix(in srgb, #fff 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-white) 10%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-white\\/20 {\n    --tw-gradient-via: color-mix(in srgb, #fff 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-white) 20%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-white\\/30 {\n    --tw-gradient-via: color-mix(in srgb, #fff 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-white) 30%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .to-indigo-100 {\n    --tw-gradient-to: var(--color-indigo-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-primary\\/10 {\n    --tw-gradient-to: color-mix(in srgb, hsl(var(--primary)) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-primary) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-primary\\/70 {\n    --tw-gradient-to: color-mix(in srgb, hsl(var(--primary)) 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-primary) 70%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-primary\\/80 {\n    --tw-gradient-to: color-mix(in srgb, hsl(var(--primary)) 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-primary) 80%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-purple-600 {\n    --tw-gradient-to: var(--color-purple-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-transparent {\n    --tw-gradient-to: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-white\\/5 {\n    --tw-gradient-to: color-mix(in srgb, #fff 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-white) 5%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-white\\/10 {\n    --tw-gradient-to: color-mix(in srgb, #fff 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-white) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .bg-clip-text {\n    background-clip: text;\n  }\n  .fill-primary {\n    fill: var(--color-primary);\n  }\n  .object-cover {\n    object-fit: cover;\n  }\n  .p-0 {\n    padding: calc(var(--spacing) * 0);\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .px-1 {\n    padding-inline: calc(var(--spacing) * 1);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .py-0\\.5 {\n    padding-block: calc(var(--spacing) * 0.5);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-1\\.5 {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-6 {\n    padding-block: calc(var(--spacing) * 6);\n  }\n  .py-8 {\n    padding-block: calc(var(--spacing) * 8);\n  }\n  .py-16 {\n    padding-block: calc(var(--spacing) * 16);\n  }\n  .pt-8 {\n    padding-top: calc(var(--spacing) * 8);\n  }\n  .pr-4 {\n    padding-right: calc(var(--spacing) * 4);\n  }\n  .pl-32 {\n    padding-left: calc(var(--spacing) * 32);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-3xl {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-6xl {\n    font-size: var(--text-6xl);\n    line-height: var(--tw-leading, var(--text-6xl--line-height));\n  }\n  .text-base {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .leading-none {\n    --tw-leading: 1;\n    line-height: 1;\n  }\n  .leading-relaxed {\n    --tw-leading: var(--leading-relaxed);\n    line-height: var(--leading-relaxed);\n  }\n  .leading-tight {\n    --tw-leading: var(--leading-tight);\n    line-height: var(--leading-tight);\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .tracking-tight {\n    --tw-tracking: var(--tracking-tight);\n    letter-spacing: var(--tracking-tight);\n  }\n  .text-balance {\n    text-wrap: balance;\n  }\n  .break-words {\n    overflow-wrap: break-word;\n  }\n  .whitespace-nowrap {\n    white-space: nowrap;\n  }\n  .text-blue-600 {\n    color: var(--color-blue-600);\n  }\n  .text-card-foreground {\n    color: var(--color-card-foreground);\n  }\n  .text-foreground {\n    color: var(--color-foreground);\n  }\n  .text-gray-500 {\n    color: var(--color-gray-500);\n  }\n  .text-gray-600 {\n    color: var(--color-gray-600);\n  }\n  .text-gray-700 {\n    color: var(--color-gray-700);\n  }\n  .text-gray-900 {\n    color: var(--color-gray-900);\n  }\n  .text-green-600 {\n    color: var(--color-green-600);\n  }\n  .text-green-700 {\n    color: var(--color-green-700);\n  }\n  .text-muted-foreground {\n    color: var(--color-muted-foreground);\n  }\n  .text-primary {\n    color: var(--color-primary);\n  }\n  .text-primary-foreground {\n    color: var(--color-primary-foreground);\n  }\n  .text-purple-600 {\n    color: var(--color-purple-600);\n  }\n  .text-red-500 {\n    color: var(--color-red-500);\n  }\n  .text-red-600 {\n    color: var(--color-red-600);\n  }\n  .text-red-700 {\n    color: var(--color-red-700);\n  }\n  .text-secondary-foreground {\n    color: var(--color-secondary-foreground);\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .text-white\\/90 {\n    color: color-mix(in srgb, #fff 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 90%, transparent);\n    }\n  }\n  .text-yellow-400 {\n    color: var(--color-yellow-400);\n  }\n  .lowercase {\n    text-transform: lowercase;\n  }\n  .italic {\n    font-style: italic;\n  }\n  .underline {\n    text-decoration-line: underline;\n  }\n  .underline-offset-4 {\n    text-underline-offset: 4px;\n  }\n  .antialiased {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  .opacity-0 {\n    opacity: 0%;\n  }\n  .opacity-20 {\n    opacity: 20%;\n  }\n  .opacity-50 {\n    opacity: 50%;\n  }\n  .opacity-60 {\n    opacity: 60%;\n  }\n  .opacity-75 {\n    opacity: 75%;\n  }\n  .opacity-90 {\n    opacity: 90%;\n  }\n  .opacity-100 {\n    opacity: 100%;\n  }\n  .shadow-2xl {\n    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-none {\n    --tw-shadow: 0 0 #0000;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-soft {\n    --tw-shadow: 0 2px 8px var(--tw-shadow-color, rgba(0, 0, 0, 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-strong {\n    --tw-shadow: 0 8px 32px var(--tw-shadow-color, rgba(0, 0, 0, 0.2));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xl {\n    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xs {\n    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-2 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-4 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-primary\\/10 {\n    --tw-ring-color: color-mix(in srgb, hsl(var(--primary)) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-ring-color: color-mix(in oklab, var(--color-primary) 10%, transparent);\n    }\n  }\n  .ring-primary\\/20 {\n    --tw-ring-color: color-mix(in srgb, hsl(var(--primary)) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-ring-color: color-mix(in oklab, var(--color-primary) 20%, transparent);\n    }\n  }\n  .ring-ring {\n    --tw-ring-color: var(--color-ring);\n  }\n  .ring-offset-2 {\n    --tw-ring-offset-width: 2px;\n    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  }\n  .outline {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n  .blur {\n    --tw-blur: blur(8px);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .drop-shadow-lg {\n    --tw-drop-shadow-size: drop-shadow(0 4px 4px var(--tw-drop-shadow-color, rgb(0 0 0 / 0.15)));\n    --tw-drop-shadow: drop-shadow(var(--drop-shadow-lg));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .drop-shadow-sm {\n    --tw-drop-shadow-size: drop-shadow(0 1px 2px var(--tw-drop-shadow-color, rgb(0 0 0 / 0.15)));\n    --tw-drop-shadow: drop-shadow(var(--drop-shadow-sm));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .backdrop-blur-sm {\n    --tw-backdrop-blur: blur(var(--blur-sm));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-xs {\n    --tw-backdrop-blur: blur(var(--blur-xs));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[color\\,box-shadow\\] {\n    transition-property: color,box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .duration-75 {\n    --tw-duration: 75ms;\n    transition-duration: 75ms;\n  }\n  .duration-150 {\n    --tw-duration: 150ms;\n    transition-duration: 150ms;\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .duration-500 {\n    --tw-duration: 500ms;\n    transition-duration: 500ms;\n  }\n  .duration-700 {\n    --tw-duration: 700ms;\n    transition-duration: 700ms;\n  }\n  .ease-in-out {\n    --tw-ease: var(--ease-in-out);\n    transition-timing-function: var(--ease-in-out);\n  }\n  .ease-out {\n    --tw-ease: var(--ease-out);\n    transition-timing-function: var(--ease-out);\n  }\n  .ease-spring {\n    --tw-ease: var(--ease-spring);\n    transition-timing-function: var(--ease-spring);\n  }\n  .outline-none {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n  .group-hover\\:translate-x-full {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-translate-x: 100%;\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .group-hover\\:translate-y-0 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-translate-y: calc(var(--spacing) * 0);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .group-hover\\:translate-y-\\[-2px\\] {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-translate-y: -2px;\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .group-hover\\:scale-105 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-scale-x: 105%;\n        --tw-scale-y: 105%;\n        --tw-scale-z: 105%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .group-hover\\:scale-110 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-scale-x: 110%;\n        --tw-scale-y: 110%;\n        --tw-scale-z: 110%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .group-hover\\:opacity-40 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        opacity: 40%;\n      }\n    }\n  }\n  .group-hover\\:opacity-80 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        opacity: 80%;\n      }\n    }\n  }\n  .group-hover\\:opacity-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-hover\\:ring-primary\\/20 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-ring-color: color-mix(in srgb, hsl(var(--primary)) 20%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--color-primary) 20%, transparent);\n        }\n      }\n    }\n  }\n  .group-hover\\/btn\\:scale-110 {\n    &:is(:where(.group\\/btn):hover *) {\n      @media (hover: hover) {\n        --tw-scale-x: 110%;\n        --tw-scale-y: 110%;\n        --tw-scale-z: 110%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .group-active\\:scale-95 {\n    &:is(:where(.group):active *) {\n      --tw-scale-x: 95%;\n      --tw-scale-y: 95%;\n      --tw-scale-z: 95%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .group-active\\:scale-100 {\n    &:is(:where(.group):active *) {\n      --tw-scale-x: 100%;\n      --tw-scale-y: 100%;\n      --tw-scale-z: 100%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .group-active\\:duration-75 {\n    &:is(:where(.group):active *) {\n      --tw-duration: 75ms;\n      transition-duration: 75ms;\n    }\n  }\n  .hover\\:scale-105 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 105%;\n        --tw-scale-y: 105%;\n        --tw-scale-z: 105%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:scale-110 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 110%;\n        --tw-scale-y: 110%;\n        --tw-scale-z: 110%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:scale-\\[1\\.01\\] {\n    &:hover {\n      @media (hover: hover) {\n        scale: 1.01;\n      }\n    }\n  }\n  .hover\\:scale-\\[1\\.02\\] {\n    &:hover {\n      @media (hover: hover) {\n        scale: 1.02;\n      }\n    }\n  }\n  .hover\\:border-border {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--color-border);\n      }\n    }\n  }\n  .hover\\:border-primary\\/40 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: color-mix(in srgb, hsl(var(--primary)) 40%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--color-primary) 40%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-accent);\n      }\n    }\n  }\n  .hover\\:bg-background {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-background);\n      }\n    }\n  }\n  .hover\\:bg-destructive\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, hsl(var(--destructive)) 90%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-destructive) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-green-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-100);\n      }\n    }\n  }\n  .hover\\:bg-primary {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-primary);\n      }\n    }\n  }\n  .hover\\:bg-primary\\/5 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, hsl(var(--primary)) 5%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-primary) 5%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, hsl(var(--primary)) 20%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-primary) 20%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, hsl(var(--primary)) 90%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-secondary {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-secondary);\n      }\n    }\n  }\n  .hover\\:bg-secondary\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, hsl(var(--secondary)) 80%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-secondary) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gradient-to-r {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-position: to right in oklab;\n        background-image: linear-gradient(var(--tw-gradient-stops));\n      }\n    }\n  }\n  .hover\\:from-primary\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: color-mix(in srgb, hsl(var(--primary)) 90%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-from: color-mix(in oklab, var(--color-primary) 90%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-primary\\/60 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: color-mix(in srgb, hsl(var(--primary)) 60%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-to: color-mix(in oklab, var(--color-primary) 60%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:text-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-accent-foreground);\n      }\n    }\n  }\n  .hover\\:text-blue-500 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-blue-500);\n      }\n    }\n  }\n  .hover\\:text-gray-700 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-gray-700);\n      }\n    }\n  }\n  .hover\\:text-primary {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-primary);\n      }\n    }\n  }\n  .hover\\:text-primary-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-primary-foreground);\n      }\n    }\n  }\n  .hover\\:text-secondary-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-secondary-foreground);\n      }\n    }\n  }\n  .hover\\:underline {\n    &:hover {\n      @media (hover: hover) {\n        text-decoration-line: underline;\n      }\n    }\n  }\n  .hover\\:shadow-2xl {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-lg {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-medium {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 4px 16px var(--tw-shadow-color, rgba(0, 0, 0, 0.15));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-strong {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 8px 32px var(--tw-shadow-color, rgba(0, 0, 0, 0.2));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-xl {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:ring-8 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(8px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:ring-primary\\/40 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-ring-color: color-mix(in srgb, hsl(var(--primary)) 40%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--color-primary) 40%, transparent);\n        }\n      }\n    }\n  }\n  .focus\\:border-transparent {\n    &:focus {\n      border-color: transparent;\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-blue-500 {\n    &:focus {\n      --tw-ring-color: var(--color-blue-500);\n    }\n  }\n  .focus\\:ring-offset-2 {\n    &:focus {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus\\:outline-hidden {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .focus-visible\\:border-ring {\n    &:focus-visible {\n      border-color: var(--color-ring);\n    }\n  }\n  .focus-visible\\:ring-2 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-\\[3px\\] {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-destructive\\/20 {\n    &:focus-visible {\n      --tw-ring-color: color-mix(in srgb, hsl(var(--destructive)) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-primary {\n    &:focus-visible {\n      --tw-ring-color: var(--color-primary);\n    }\n  }\n  .focus-visible\\:ring-ring {\n    &:focus-visible {\n      --tw-ring-color: var(--color-ring);\n    }\n  }\n  .focus-visible\\:ring-ring\\/50 {\n    &:focus-visible {\n      --tw-ring-color: color-mix(in srgb, hsl(var(--ring)) 50%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-offset-2 {\n    &:focus-visible {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus-visible\\:outline-none {\n    &:focus-visible {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .active\\:scale-95 {\n    &:active {\n      --tw-scale-x: 95%;\n      --tw-scale-y: 95%;\n      --tw-scale-z: 95%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .active\\:scale-\\[0\\.98\\] {\n    &:active {\n      scale: 0.98;\n    }\n  }\n  .active\\:shadow-sm {\n    &:active {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .disabled\\:pointer-events-none {\n    &:disabled {\n      pointer-events: none;\n    }\n  }\n  .disabled\\:cursor-not-allowed {\n    &:disabled {\n      cursor: not-allowed;\n    }\n  }\n  .disabled\\:opacity-30 {\n    &:disabled {\n      opacity: 30%;\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .disabled\\:hover\\:scale-100 {\n    &:disabled {\n      &:hover {\n        @media (hover: hover) {\n          --tw-scale-x: 100%;\n          --tw-scale-y: 100%;\n          --tw-scale-z: 100%;\n          scale: var(--tw-scale-x) var(--tw-scale-y);\n        }\n      }\n    }\n  }\n  .has-data-\\[slot\\=card-action\\]\\:grid-cols-\\[1fr_auto\\] {\n    &:has(*[data-slot=\"card-action\"]) {\n      grid-template-columns: 1fr auto;\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-2\\.5 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 2.5);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-3 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 3);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-4 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 4);\n    }\n  }\n  .aria-invalid\\:border-destructive {\n    &[aria-invalid=\"true\"] {\n      border-color: var(--color-destructive);\n    }\n  }\n  .aria-invalid\\:ring-destructive\\/20 {\n    &[aria-invalid=\"true\"] {\n      --tw-ring-color: color-mix(in srgb, hsl(var(--destructive)) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);\n      }\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:h-px {\n    &[data-orientation=\"horizontal\"] {\n      height: 1px;\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:w-full {\n    &[data-orientation=\"horizontal\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:h-full {\n    &[data-orientation=\"vertical\"] {\n      height: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-px {\n    &[data-orientation=\"vertical\"] {\n      width: 1px;\n    }\n  }\n  .sm\\:mt-8 {\n    @media (width >= 40rem) {\n      margin-top: calc(var(--spacing) * 8);\n    }\n  }\n  .sm\\:h-5 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .sm\\:h-7 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 7);\n    }\n  }\n  .sm\\:h-12 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 12);\n    }\n  }\n  .sm\\:h-14 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 14);\n    }\n  }\n  .sm\\:h-16 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 16);\n    }\n  }\n  .sm\\:h-28 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 28);\n    }\n  }\n  .sm\\:h-80 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 80);\n    }\n  }\n  .sm\\:h-96 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 96);\n    }\n  }\n  .sm\\:min-h-18 {\n    @media (width >= 40rem) {\n      min-height: calc(var(--spacing) * 18);\n    }\n  }\n  .sm\\:w-5 {\n    @media (width >= 40rem) {\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .sm\\:w-7 {\n    @media (width >= 40rem) {\n      width: calc(var(--spacing) * 7);\n    }\n  }\n  .sm\\:w-12 {\n    @media (width >= 40rem) {\n      width: calc(var(--spacing) * 12);\n    }\n  }\n  .sm\\:w-14 {\n    @media (width >= 40rem) {\n      width: calc(var(--spacing) * 14);\n    }\n  }\n  .sm\\:w-28 {\n    @media (width >= 40rem) {\n      width: calc(var(--spacing) * 28);\n    }\n  }\n  .sm\\:max-w-lg {\n    @media (width >= 40rem) {\n      max-width: var(--container-lg);\n    }\n  }\n  .sm\\:flex-\\[0_0_85\\%\\] {\n    @media (width >= 40rem) {\n      flex: 0 0 85%;\n    }\n  }\n  .sm\\:flex-row {\n    @media (width >= 40rem) {\n      flex-direction: row;\n    }\n  }\n  .sm\\:gap-4 {\n    @media (width >= 40rem) {\n      gap: calc(var(--spacing) * 4);\n    }\n  }\n  .sm\\:space-y-4 {\n    @media (width >= 40rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-y-reverse: 0;\n        margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n        margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n      }\n    }\n  }\n  .sm\\:space-x-2 {\n    @media (width >= 40rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-x-reverse: 0;\n        margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n        margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n      }\n    }\n  }\n  .sm\\:space-x-3 {\n    @media (width >= 40rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-x-reverse: 0;\n        margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n        margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n      }\n    }\n  }\n  .sm\\:space-x-4 {\n    @media (width >= 40rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-x-reverse: 0;\n        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n      }\n    }\n  }\n  .sm\\:rounded-2xl {\n    @media (width >= 40rem) {\n      border-radius: var(--radius-2xl);\n    }\n  }\n  .sm\\:p-6 {\n    @media (width >= 40rem) {\n      padding: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:p-8 {\n    @media (width >= 40rem) {\n      padding: calc(var(--spacing) * 8);\n    }\n  }\n  .sm\\:px-2 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .sm\\:px-6 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:py-3 {\n    @media (width >= 40rem) {\n      padding-block: calc(var(--spacing) * 3);\n    }\n  }\n  .sm\\:text-2xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-2xl);\n      line-height: var(--tw-leading, var(--text-2xl--line-height));\n    }\n  }\n  .sm\\:text-3xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-3xl);\n      line-height: var(--tw-leading, var(--text-3xl--line-height));\n    }\n  }\n  .sm\\:text-5xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-5xl);\n      line-height: var(--tw-leading, var(--text-5xl--line-height));\n    }\n  }\n  .sm\\:text-base {\n    @media (width >= 40rem) {\n      font-size: var(--text-base);\n      line-height: var(--tw-leading, var(--text-base--line-height));\n    }\n  }\n  .sm\\:text-lg {\n    @media (width >= 40rem) {\n      font-size: var(--text-lg);\n      line-height: var(--tw-leading, var(--text-lg--line-height));\n    }\n  }\n  .sm\\:text-sm {\n    @media (width >= 40rem) {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .sm\\:text-xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-xl);\n      line-height: var(--tw-leading, var(--text-xl--line-height));\n    }\n  }\n  .sm\\:leading-loose {\n    @media (width >= 40rem) {\n      --tw-leading: var(--leading-loose);\n      line-height: var(--leading-loose);\n    }\n  }\n  .md\\:flex-\\[0_0_80\\%\\] {\n    @media (width >= 48rem) {\n      flex: 0 0 80%;\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-3 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:mb-12 {\n    @media (width >= 64rem) {\n      margin-bottom: calc(var(--spacing) * 12);\n    }\n  }\n  .lg\\:h-6 {\n    @media (width >= 64rem) {\n      height: calc(var(--spacing) * 6);\n    }\n  }\n  .lg\\:h-18 {\n    @media (width >= 64rem) {\n      height: calc(var(--spacing) * 18);\n    }\n  }\n  .lg\\:h-32 {\n    @media (width >= 64rem) {\n      height: calc(var(--spacing) * 32);\n    }\n  }\n  .lg\\:h-96 {\n    @media (width >= 64rem) {\n      height: calc(var(--spacing) * 96);\n    }\n  }\n  .lg\\:min-h-20 {\n    @media (width >= 64rem) {\n      min-height: calc(var(--spacing) * 20);\n    }\n  }\n  .lg\\:w-6 {\n    @media (width >= 64rem) {\n      width: calc(var(--spacing) * 6);\n    }\n  }\n  .lg\\:w-32 {\n    @media (width >= 64rem) {\n      width: calc(var(--spacing) * 32);\n    }\n  }\n  .lg\\:max-w-2xl {\n    @media (width >= 64rem) {\n      max-width: var(--container-2xl);\n    }\n  }\n  .lg\\:max-w-xl {\n    @media (width >= 64rem) {\n      max-width: var(--container-xl);\n    }\n  }\n  .lg\\:flex-\\[0_0_360px\\] {\n    @media (width >= 64rem) {\n      flex: 0 0 360px;\n    }\n  }\n  .lg\\:grid-cols-2 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:gap-6 {\n    @media (width >= 64rem) {\n      gap: calc(var(--spacing) * 6);\n    }\n  }\n  .lg\\:p-8 {\n    @media (width >= 64rem) {\n      padding: calc(var(--spacing) * 8);\n    }\n  }\n  .lg\\:px-4 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 4);\n    }\n  }\n  .lg\\:py-4 {\n    @media (width >= 64rem) {\n      padding-block: calc(var(--spacing) * 4);\n    }\n  }\n  .lg\\:text-2xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-2xl);\n      line-height: var(--tw-leading, var(--text-2xl--line-height));\n    }\n  }\n  .lg\\:text-3xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-3xl);\n      line-height: var(--tw-leading, var(--text-3xl--line-height));\n    }\n  }\n  .lg\\:text-4xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-4xl);\n      line-height: var(--tw-leading, var(--text-4xl--line-height));\n    }\n  }\n  .lg\\:text-6xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-6xl);\n      line-height: var(--tw-leading, var(--text-6xl--line-height));\n    }\n  }\n  .lg\\:text-base {\n    @media (width >= 64rem) {\n      font-size: var(--text-base);\n      line-height: var(--tw-leading, var(--text-base--line-height));\n    }\n  }\n  .lg\\:text-lg {\n    @media (width >= 64rem) {\n      font-size: var(--text-lg);\n      line-height: var(--tw-leading, var(--text-lg--line-height));\n    }\n  }\n  .dark\\:border-gray-600 {\n    &:is(.dark *) {\n      border-color: var(--color-gray-600);\n    }\n  }\n  .dark\\:border-gray-700 {\n    &:is(.dark *) {\n      border-color: var(--color-gray-700);\n    }\n  }\n  .dark\\:border-input {\n    &:is(.dark *) {\n      border-color: var(--color-input);\n    }\n  }\n  .dark\\:bg-blue-900 {\n    &:is(.dark *) {\n      background-color: var(--color-blue-900);\n    }\n  }\n  .dark\\:bg-destructive\\/60 {\n    &:is(.dark *) {\n      background-color: color-mix(in srgb, hsl(var(--destructive)) 60%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-destructive) 60%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-gray-700 {\n    &:is(.dark *) {\n      background-color: var(--color-gray-700);\n    }\n  }\n  .dark\\:bg-gray-800 {\n    &:is(.dark *) {\n      background-color: var(--color-gray-800);\n    }\n  }\n  .dark\\:bg-gray-900 {\n    &:is(.dark *) {\n      background-color: var(--color-gray-900);\n    }\n  }\n  .dark\\:bg-green-900 {\n    &:is(.dark *) {\n      background-color: var(--color-green-900);\n    }\n  }\n  .dark\\:bg-input\\/30 {\n    &:is(.dark *) {\n      background-color: color-mix(in srgb, hsl(var(--input)) 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-input) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-purple-900 {\n    &:is(.dark *) {\n      background-color: var(--color-purple-900);\n    }\n  }\n  .dark\\:from-gray-900 {\n    &:is(.dark *) {\n      --tw-gradient-from: var(--color-gray-900);\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-gray-800 {\n    &:is(.dark *) {\n      --tw-gradient-to: var(--color-gray-800);\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:text-gray-100 {\n    &:is(.dark *) {\n      color: var(--color-gray-100);\n    }\n  }\n  .dark\\:text-gray-300 {\n    &:is(.dark *) {\n      color: var(--color-gray-300);\n    }\n  }\n  .dark\\:text-gray-400 {\n    &:is(.dark *) {\n      color: var(--color-gray-400);\n    }\n  }\n  .dark\\:text-white {\n    &:is(.dark *) {\n      color: var(--color-white);\n    }\n  }\n  .dark\\:hover\\:bg-accent\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: color-mix(in srgb, hsl(var(--accent)) 50%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--color-accent) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:bg-input\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: color-mix(in srgb, hsl(var(--input)) 50%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--color-input) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:text-gray-300 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--color-gray-300);\n        }\n      }\n    }\n  }\n  .dark\\:focus-visible\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &:focus-visible {\n        --tw-ring-color: color-mix(in srgb, hsl(var(--destructive)) 40%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:aria-invalid\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &[aria-invalid=\"true\"] {\n        --tw-ring-color: color-mix(in srgb, hsl(var(--destructive)) 40%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .\\[\\&_svg\\]\\:pointer-events-none {\n    & svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&_svg\\]\\:shrink-0 {\n    & svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 {\n    & svg:not([class*='size-']) {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\.border-b\\]\\:pb-6 {\n    &:is(.border-b) {\n      padding-bottom: calc(var(--spacing) * 6);\n    }\n  }\n  .\\[\\.border-t\\]\\:pt-6 {\n    &:is(.border-t) {\n      padding-top: calc(var(--spacing) * 6);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:pointer-events-none {\n    &>svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-3 {\n    &>svg {\n      width: calc(var(--spacing) * 3);\n      height: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-accent {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--color-accent);\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-destructive\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: color-mix(in srgb, hsl(var(--destructive)) 90%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--color-destructive) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-primary\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: color-mix(in srgb, hsl(var(--primary)) 90%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-secondary\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: color-mix(in srgb, hsl(var(--secondary)) 90%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--color-secondary) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:text-accent-foreground {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--color-accent-foreground);\n        }\n      }\n    }\n  }\n}\n@layer base {\n  *,\n  ::after,\n  ::before,\n  ::backdrop,\n  ::file-selector-button {\n    border-color: var(--color-gray-200, currentcolor);\n  }\n}\n@layer base {\n  :root {\n    --background: 0 0% 100%;\n    --foreground: 0 0% 3.9%;\n    --card: 0 0% 100%;\n    --card-foreground: 0 0% 3.9%;\n    --popover: 0 0% 100%;\n    --popover-foreground: 0 0% 3.9%;\n    --primary: 221.2 83.2% 53.3%;\n    --primary-foreground: 210 40% 98%;\n    --secondary: 210 40% 96%;\n    --secondary-foreground: 222.2 84% 4.9%;\n    --muted: 210 40% 96%;\n    --muted-foreground: 215.4 16.3% 46.9%;\n    --accent: 210 40% 96%;\n    --accent-foreground: 222.2 84% 4.9%;\n    --destructive: 0 84.2% 60.2%;\n    --destructive-foreground: 210 40% 98%;\n    --border: 214.3 31.8% 91.4%;\n    --input: 214.3 31.8% 91.4%;\n    --ring: 221.2 83.2% 53.3%;\n    --radius: 0.5rem;\n  }\n  .dark {\n    --background: 222.2 84% 4.9%;\n    --foreground: 210 40% 98%;\n    --card: 222.2 84% 4.9%;\n    --card-foreground: 210 40% 98%;\n    --popover: 222.2 84% 4.9%;\n    --popover-foreground: 210 40% 98%;\n    --primary: 217.2 91.2% 59.8%;\n    --primary-foreground: 222.2 84% 4.9%;\n    --secondary: 217.2 32.6% 17.5%;\n    --secondary-foreground: 210 40% 98%;\n    --muted: 217.2 32.6% 17.5%;\n    --muted-foreground: 215 20.2% 65.1%;\n    --accent: 217.2 32.6% 17.5%;\n    --accent-foreground: 210 40% 98%;\n    --destructive: 0 62.8% 30.6%;\n    --destructive-foreground: 210 40% 98%;\n    --border: 217.2 32.6% 17.5%;\n    --input: 217.2 32.6% 17.5%;\n    --ring: 224.3 76.3% 94.1%;\n  }\n}\n@layer base {\n  * {\n    border-color: var(--color-border);\n  }\n  body {\n    background-color: var(--color-background);\n    color: var(--color-foreground);\n    font-feature-settings: \"rlig\" 1, \"calt\" 1;\n  }\n}\n@layer utilities {\n  .shadow-soft {\n    box-shadow: var(--shadow-soft);\n  }\n  .shadow-medium {\n    box-shadow: var(--shadow-medium);\n  }\n  .shadow-strong {\n    box-shadow: var(--shadow-strong);\n  }\n  .ease-spring {\n    transition-timing-function: var(--ease-spring);\n  }\n  .animate-fade-in {\n    animation: var(--animate-fade-in);\n  }\n  .transform-gpu {\n    transform: translateZ(0);\n  }\n  .line-clamp-1 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 1;\n  }\n  .line-clamp-2 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 2;\n  }\n  .line-clamp-3 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 3;\n  }\n  .line-clamp-4 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 4;\n  }\n  .embla {\n    overflow: hidden;\n  }\n  .embla__container {\n    display: flex;\n  }\n  .embla__slide {\n    flex: 0 0 auto;\n    min-width: 0;\n  }\n  .focus-ring {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n    &:focus {\n      --tw-ring-color: var(--color-primary);\n    }\n    &:focus {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n    &:focus {\n      --tw-ring-offset-color: var(--color-background);\n    }\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .btn-hover-lift {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 105%;\n        --tw-scale-y: 105%;\n        --tw-scale-z: 105%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .glass {\n    backdrop-filter: blur(16px);\n    -webkit-backdrop-filter: blur(16px);\n  }\n  .glass-light {\n    backdrop-filter: blur(8px);\n    -webkit-backdrop-filter: blur(8px);\n  }\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-gradient-position {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-via {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-to {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-via-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 0%;\n}\n@property --tw-gradient-via-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 50%;\n}\n@property --tw-gradient-to-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes ping {\n  75%, 100% {\n    transform: scale(2);\n    opacity: 0;\n  }\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@keyframes fadeIn {\n  0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-gradient-position: initial;\n      --tw-gradient-from: #0000;\n      --tw-gradient-via: #0000;\n      --tw-gradient-to: #0000;\n      --tw-gradient-stops: initial;\n      --tw-gradient-via-stops: initial;\n      --tw-gradient-from-position: 0%;\n      --tw-gradient-via-position: 50%;\n      --tw-gradient-to-position: 100%;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-tracking: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n      --tw-ease: initial;\n    }\n  }\n}"], "names": [], "mappings": "AACA;EAsuGE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAtuGJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFF;EAwHE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAogFA;;;;EASA;;;;;;;;;;;;;;;;;;;;;;;EAsBA;;;;;;;;;;;;;;;;;;;;;;EAuBA;;;;EAGA;;;;;;;AAj0FF;;AAAA;EAyQE;;;;EAIA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EASA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAMA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EASA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;;EAEE;IAAiE;;;;;EAKnE;;;;;EAEE;IAAiE;;;;;EAKnE;;;;;EAEE;IAAiE;;;;;EAKnE;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAMI;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;;;;EASzB;;;;;;;EAQA;;;;;;;EAQA;;;;;EAOE;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;;EAQvB;IAAuB;;;;;IAErB;MAAgD;;;;;;EASlD;IAAuB;;;;;IAErB;MAAgD;;;;;;EASlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAOpD;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAMA;;;;;;;EAQA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAOI;IAAuB;;;;;;;;EAU3B;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IACE;;;;;;;EAQF;IACE;;;;;;;EAQF;IACE;;;;;;;EAQF;IACE;;;;;;;EAQF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAOI;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;;EAQzB;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAOpD;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAQI;IAAuB;;;;;EASvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;;EAwE7B;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;EAKE;;;;;;;;;;;EAmBF;;;;;;;;EAOI;IAAuB;;;;;;;;EAQvB;IAAuB;;;;;;EAM3B;;;;EAIA;;;;;AAKF;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;;AAMA;;;;;;AAKA", "debugId": null}}]}