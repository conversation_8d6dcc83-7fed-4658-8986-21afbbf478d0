import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const progressVariants = cva(
  "relative h-2 w-full overflow-hidden rounded-full bg-secondary",
  {
    variants: {
      size: {
        sm: "h-1",
        md: "h-2",
        lg: "h-3",
        xl: "h-4",
      },
      variant: {
        default: "bg-secondary",
        success: "bg-success/20",
        warning: "bg-warning/20",
        destructive: "bg-destructive/20",
        info: "bg-info/20",
      },
    },
    defaultVariants: {
      size: "md",
      variant: "default",
    },
  }
)

const progressIndicatorVariants = cva(
  "h-full w-full flex-1 bg-primary transition-all duration-300 ease-out",
  {
    variants: {
      variant: {
        default: "bg-primary",
        success: "bg-success",
        warning: "bg-warning",
        destructive: "bg-destructive",
        info: "bg-info",
      },
      animated: {
        true: "animate-pulse",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      animated: false,
    },
  }
)

export interface ProgressProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>,
    VariantProps<typeof progressVariants> {
  value?: number
  max?: number
  showValue?: boolean
  label?: string
  animated?: boolean
  indicatorVariant?: VariantProps<typeof progressIndicatorVariants>['variant']
}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(({ 
  className, 
  value = 0, 
  max = 100,
  size, 
  variant, 
  showValue = false,
  label,
  animated = false,
  indicatorVariant,
  ...props 
}, ref) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  const displayVariant = indicatorVariant || variant

  return (
    <div className="w-full space-y-2">
      {(label || showValue) && (
        <div className="flex justify-between items-center text-sm">
          {label && <span className="font-medium text-foreground">{label}</span>}
          {showValue && (
            <span className="text-muted-foreground">
              {Math.round(percentage)}%
            </span>
          )}
        </div>
      )}
      
      <ProgressPrimitive.Root
        ref={ref}
        className={cn(progressVariants({ size, variant }), className)}
        value={value}
        max={max}
        {...props}
      >
        <ProgressPrimitive.Indicator
          className={cn(
            progressIndicatorVariants({ 
              variant: displayVariant, 
              animated 
            })
          )}
          style={{ transform: `translateX(-${100 - percentage}%)` }}
        />
      </ProgressPrimitive.Root>
    </div>
  )
})
Progress.displayName = ProgressPrimitive.Root.displayName

// Circular Progress Component
interface CircularProgressProps {
  value?: number
  max?: number
  size?: number
  strokeWidth?: number
  className?: string
  showValue?: boolean
  variant?: VariantProps<typeof progressIndicatorVariants>['variant']
  animated?: boolean
}

const CircularProgress = React.forwardRef<HTMLDivElement, CircularProgressProps>(
  ({ 
    value = 0, 
    max = 100, 
    size = 120, 
    strokeWidth = 8, 
    className,
    showValue = false,
    variant = "default",
    animated = false,
    ...props 
  }, ref) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
    const radius = (size - strokeWidth) / 2
    const circumference = radius * 2 * Math.PI
    const strokeDasharray = circumference
    const strokeDashoffset = circumference - (percentage / 100) * circumference

    const getStrokeColor = () => {
      switch (variant) {
        case 'success': return 'hsl(var(--color-success))'
        case 'warning': return 'hsl(var(--color-warning))'
        case 'destructive': return 'hsl(var(--color-destructive))'
        case 'info': return 'hsl(var(--color-info))'
        default: return 'hsl(var(--primary))'
      }
    }

    return (
      <div 
        ref={ref}
        className={cn("relative inline-flex items-center justify-center", className)}
        style={{ width: size, height: size }}
        {...props}
      >
        <svg
          width={size}
          height={size}
          className="transform -rotate-90"
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="hsl(var(--muted))"
            strokeWidth={strokeWidth}
            fill="transparent"
            className="opacity-20"
          />
          
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={getStrokeColor()}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className={cn(
              "transition-all duration-500 ease-out",
              animated && "animate-pulse"
            )}
          />
        </svg>
        
        {showValue && (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-sm font-medium text-foreground">
              {Math.round(percentage)}%
            </span>
          </div>
        )}
      </div>
    )
  }
)
CircularProgress.displayName = "CircularProgress"

// Multi-step Progress Component
interface Step {
  id: string
  label: string
  description?: string
  completed?: boolean
  current?: boolean
  error?: boolean
}

interface StepProgressProps {
  steps: Step[]
  className?: string
  orientation?: 'horizontal' | 'vertical'
  showLabels?: boolean
  showDescriptions?: boolean
}

const StepProgress = React.forwardRef<HTMLDivElement, StepProgressProps>(
  ({ 
    steps, 
    className, 
    orientation = 'horizontal',
    showLabels = true,
    showDescriptions = false,
    ...props 
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex",
          orientation === 'horizontal' ? "items-center space-x-4" : "flex-col space-y-4",
          className
        )}
        {...props}
      >
        {steps.map((step, index) => (
          <div
            key={step.id}
            className={cn(
              "flex items-center",
              orientation === 'vertical' && "flex-col text-center"
            )}
          >
            {/* Step indicator */}
            <div className="relative">
              <div
                className={cn(
                  "flex h-8 w-8 items-center justify-center rounded-full border-2 text-sm font-medium transition-colors",
                  step.completed && "bg-success border-success text-success-foreground",
                  step.current && !step.completed && "bg-primary border-primary text-primary-foreground",
                  step.error && "bg-destructive border-destructive text-destructive-foreground",
                  !step.completed && !step.current && !step.error && "bg-muted border-muted-foreground/25 text-muted-foreground"
                )}
              >
                {step.completed ? (
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : step.error ? (
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  index + 1
                )}
              </div>
            </div>

            {/* Step content */}
            {(showLabels || showDescriptions) && (
              <div className={cn(
                "ml-3",
                orientation === 'vertical' && "ml-0 mt-2"
              )}>
                {showLabels && (
                  <div className={cn(
                    "text-sm font-medium",
                    step.completed && "text-success",
                    step.current && "text-primary",
                    step.error && "text-destructive",
                    !step.completed && !step.current && !step.error && "text-muted-foreground"
                  )}>
                    {step.label}
                  </div>
                )}
                {showDescriptions && step.description && (
                  <div className="text-xs text-muted-foreground mt-1">
                    {step.description}
                  </div>
                )}
              </div>
            )}

            {/* Connector line */}
            {index < steps.length - 1 && (
              <div
                className={cn(
                  "flex-1 h-px bg-muted-foreground/25 mx-4",
                  orientation === 'vertical' && "w-px h-8 mx-0 my-2"
                )}
              />
            )}
          </div>
        ))}
      </div>
    )
  }
)
StepProgress.displayName = "StepProgress"

export { 
  Progress, 
  CircularProgress, 
  StepProgress,
  progressVariants,
  type Step,
  type ProgressProps,
  type CircularProgressProps,
  type StepProgressProps,
}
