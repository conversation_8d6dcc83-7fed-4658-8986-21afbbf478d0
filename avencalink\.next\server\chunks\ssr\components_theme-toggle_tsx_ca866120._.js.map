{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Moon, Sun } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface ThemeToggleProps {\r\n  className?: string;\r\n  variant?: \"default\" | \"compact\" | \"icon-only\";\r\n}\r\n\r\nexport function ThemeToggle({\r\n  className,\r\n  variant = \"default\",\r\n}: ThemeToggleProps) {\r\n  const { setTheme, resolvedTheme } = useTheme();\r\n  const [mounted, setMounted] = React.useState(false);\r\n\r\n  React.useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) {\r\n    // Render a static placeholder to prevent layout shift and hydration errors\r\n    return (\r\n      <div\r\n        className={cn(\r\n          variant === \"icon-only\" ? \"h-9 w-9\" : \"h-8 w-16\",\r\n          \"rounded-full bg-muted animate-pulse\",\r\n          className\r\n        )}\r\n      />\r\n    );\r\n  }\r\n\r\n  const isDark = resolvedTheme === \"dark\";\r\n\r\n  const spring = {\r\n    type: \"spring\",\r\n    stiffness: 700,\r\n    damping: 30,\r\n  };\r\n\r\n  const iconVariants = {\r\n    initial: { scale: 0, rotate: -180 },\r\n    animate: { scale: 1, rotate: 0 },\r\n    exit: { scale: 0, rotate: 180 },\r\n  };\r\n\r\n  if (variant === \"icon-only\") {\r\n    return (\r\n      <motion.button\r\n        className={cn(\r\n          \"relative flex h-9 w-9 items-center justify-center rounded-lg border bg-background shadow-sm transition-all duration-200 hover:bg-accent hover:shadow-md focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n          className\r\n        )}\r\n        onClick={() => setTheme(isDark ? \"light\" : \"dark\")}\r\n        whileHover={{ scale: 1.05 }}\r\n        whileTap={{ scale: 0.95 }}\r\n        aria-label={`Switch to ${isDark ? \"light\" : \"dark\"} mode`}\r\n      >\r\n        <AnimatePresence mode=\"wait\" initial={false}>\r\n          {isDark ? (\r\n            <motion.div\r\n              key=\"moon\"\r\n              variants={iconVariants}\r\n              initial=\"initial\"\r\n              animate=\"animate\"\r\n              exit=\"exit\"\r\n              transition={spring}\r\n            >\r\n              <Moon className=\"h-4 w-4\" strokeWidth={1.5} />\r\n            </motion.div>\r\n          ) : (\r\n            <motion.div\r\n              key=\"sun\"\r\n              variants={iconVariants}\r\n              initial=\"initial\"\r\n              animate=\"animate\"\r\n              exit=\"exit\"\r\n              transition={spring}\r\n            >\r\n              <Sun className=\"h-4 w-4\" strokeWidth={1.5} />\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </motion.button>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      className={cn(\r\n        \"relative flex h-8 w-16 cursor-pointer items-center rounded-full border p-1 transition-all duration-300 hover:shadow-md\",\r\n        isDark\r\n          ? \"justify-start border-border bg-muted\"\r\n          : \"justify-end border-border bg-background\",\r\n        className\r\n      )}\r\n      onClick={() => setTheme(isDark ? \"light\" : \"dark\")}\r\n      role=\"button\"\r\n      tabIndex={0}\r\n      whileHover={{ scale: 1.02 }}\r\n      whileTap={{ scale: 0.98 }}\r\n      onKeyDown={(e) => {\r\n        if (e.key === \"Enter\" || e.key === \" \") {\r\n          setTheme(isDark ? \"light\" : \"dark\");\r\n        }\r\n      }}\r\n      aria-label={`Switch to ${isDark ? \"light\" : \"dark\"} mode`}\r\n    >\r\n      <span className=\"sr-only\">Toggle theme</span>\r\n      <motion.div\r\n        className={cn(\r\n          \"flex h-6 w-6 items-center justify-center rounded-full shadow-sm\",\r\n          isDark ? \"bg-primary\" : \"bg-background border border-border\"\r\n        )}\r\n        layout\r\n        transition={spring}\r\n      >\r\n        <AnimatePresence mode=\"wait\" initial={false}>\r\n          {isDark ? (\r\n            <motion.div\r\n              key=\"moon\"\r\n              variants={iconVariants}\r\n              initial=\"initial\"\r\n              animate=\"animate\"\r\n              exit=\"exit\"\r\n              transition={spring}\r\n            >\r\n              <Moon\r\n                className=\"h-3 w-3 text-primary-foreground\"\r\n                strokeWidth={2}\r\n              />\r\n            </motion.div>\r\n          ) : (\r\n            <motion.div\r\n              key=\"sun\"\r\n              variants={iconVariants}\r\n              initial=\"initial\"\r\n              animate=\"animate\"\r\n              exit=\"exit\"\r\n              transition={spring}\r\n            >\r\n              <Sun className=\"h-3 w-3 text-foreground\" strokeWidth={2} />\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </motion.div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAaO,SAAS,YAAY,EAC1B,SAAS,EACT,UAAU,SAAS,EACF;IACjB,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,yPAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,2EAA2E;QAC3E,qBACE,6WAAC;YACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,YAAY,cAAc,YAAY,YACtC,uCACA;;;;;;IAIR;IAEA,MAAM,SAAS,kBAAkB;IAEjC,MAAM,SAAS;QACb,MAAM;QACN,WAAW;QACX,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,SAAS;YAAE,OAAO;YAAG,QAAQ,CAAC;QAAI;QAClC,SAAS;YAAE,OAAO;YAAG,QAAQ;QAAE;QAC/B,MAAM;YAAE,OAAO;YAAG,QAAQ;QAAI;IAChC;IAEA,IAAI,YAAY,aAAa;QAC3B,qBACE,6WAAC,gSAAA,CAAA,SAAM,CAAC,MAAM;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+PACA;YAEF,SAAS,IAAM,SAAS,SAAS,UAAU;YAC3C,YAAY;gBAAE,OAAO;YAAK;YAC1B,UAAU;gBAAE,OAAO;YAAK;YACxB,cAAY,CAAC,UAAU,EAAE,SAAS,UAAU,OAAO,KAAK,CAAC;sBAEzD,cAAA,6WAAC,+RAAA,CAAA,kBAAe;gBAAC,MAAK;gBAAO,SAAS;0BACnC,uBACC,6WAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;oBAET,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,YAAY;8BAEZ,cAAA,6WAAC,sRAAA,CAAA,OAAI;wBAAC,WAAU;wBAAU,aAAa;;;;;;mBAPnC;;;;yCAUN,6WAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;oBAET,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,YAAY;8BAEZ,cAAA,6WAAC,oRAAA,CAAA,MAAG;wBAAC,WAAU;wBAAU,aAAa;;;;;;mBAPlC;;;;;;;;;;;;;;;IAahB;IAEA,qBACE,6WAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0HACA,SACI,yCACA,2CACJ;QAEF,SAAS,IAAM,SAAS,SAAS,UAAU;QAC3C,MAAK;QACL,UAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,WAAW,CAAC;YACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;gBACtC,SAAS,SAAS,UAAU;YAC9B;QACF;QACA,cAAY,CAAC,UAAU,EAAE,SAAS,UAAU,OAAO,KAAK,CAAC;;0BAEzD,6WAAC;gBAAK,WAAU;0BAAU;;;;;;0BAC1B,6WAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mEACA,SAAS,eAAe;gBAE1B,MAAM;gBACN,YAAY;0BAEZ,cAAA,6WAAC,+RAAA,CAAA,kBAAe;oBAAC,MAAK;oBAAO,SAAS;8BACnC,uBACC,6WAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;wBAET,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,MAAK;wBACL,YAAY;kCAEZ,cAAA,6WAAC,sRAAA,CAAA,OAAI;4BACH,WAAU;4BACV,aAAa;;;;;;uBATX;;;;6CAaN,6WAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;wBAET,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,MAAK;wBACL,YAAY;kCAEZ,cAAA,6WAAC,oRAAA,CAAA,MAAG;4BAAC,WAAU;4BAA0B,aAAa;;;;;;uBAPlD;;;;;;;;;;;;;;;;;;;;;AAclB", "debugId": null}}]}