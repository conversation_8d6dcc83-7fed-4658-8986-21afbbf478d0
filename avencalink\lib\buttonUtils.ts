import { ButtonConfig } from '@/types/user'
import { SECTION_BUTTON_CONFIG } from '@/types/constants'

/**
 * Gets button configuration for a section, prioritizing API data over defaults
 */
export function getButtonConfig(
    sectionType: 'features' | 'services' | 'generic',
    apiButtonConfig?: ButtonConfig
): Required<ButtonConfig> {
    const defaultConfig = SECTION_BUTTON_CONFIG[sectionType]

    return {
        primaryButtonText: apiButtonConfig?.primaryButtonText ?? defaultConfig.primaryButtonText,
        secondaryButtonText: apiButtonConfig?.secondaryButtonText ?? defaultConfig.secondaryButtonText,
        showBadge: apiButtonConfig?.showBadge ?? defaultConfig.showBadge,
    }
}

/**
 * Validates button configuration
 */
export function validateButtonConfig(config: ButtonConfig): {
    isValid: boolean
    errors: string[]
} {
    const errors: string[] = []

    if (config.primaryButtonText !== undefined && typeof config.primaryButtonText !== 'string') {
        errors.push('primaryButtonText must be a string')
    }

    if (config.secondaryButtonText !== undefined && typeof config.secondaryButtonText !== 'string') {
        errors.push('secondaryButtonText must be a string')
    }

    if (config.showBadge !== undefined && typeof config.showBadge !== 'boolean') {
        errors.push('showBadge must be a boolean')
    }

    return {
        isValid: errors.length === 0,
        errors
    }
}

/**
 * Creates default button configuration for a section type
 */
export function createDefaultButtonConfig(
    sectionType: 'features' | 'services' | 'generic'
): Required<ButtonConfig> {
    return { ...SECTION_BUTTON_CONFIG[sectionType] }
}

/**
 * Merges multiple button configurations with priority order
 */
export function mergeButtonConfigs(
    ...configs: (ButtonConfig | undefined)[]
): ButtonConfig {
    const result: ButtonConfig = {}

    for (const config of configs) {
        if (config) {
            if (config.primaryButtonText !== undefined) {
                result.primaryButtonText = config.primaryButtonText
            }
            if (config.secondaryButtonText !== undefined) {
                result.secondaryButtonText = config.secondaryButtonText
            }
            if (config.showBadge !== undefined) {
                result.showBadge = config.showBadge
            }
        }
    }

    return result
}