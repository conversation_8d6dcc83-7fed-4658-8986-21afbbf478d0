{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/theme-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\";\r\nimport * as React from \"react\";\r\n\r\nexport function ThemeProvider({\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof NextThemesProvider>) {\r\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKO,SAAS,cAAc,EAC5B,QAAQ,EACR,GAAG,OAC6C;IAChD,qBAAO,6WAAC,yPAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatUrl(url: string): string {\n  if (!url) return \"\"\n  \n  // Add https:// if no protocol is specified\n  if (!url.startsWith(\"http://\") && !url.startsWith(\"https://\")) {\n    return `https://${url}`\n  }\n  \n  return url\n}\n\nexport function validateUsername(username: string): boolean {\n  // Username validation: alphanumeric, underscores, hyphens, 3-30 characters\n  const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/\n  return usernameRegex.test(username)\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.substring(0, maxLength) + \"...\"\n}\n\nexport function generateMetaTitle(username: string, displayName?: string): string {\n  const name = displayName || username\n  return `${name} | AvencaLink`\n}\n\nexport function generateMetaDescription(bio?: string, username?: string): string {\n  if (bio) {\n    return truncateText(bio, 160)\n  }\n  return `Check out ${username}'s links on AvencaLink`\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,UAAU,GAAW;IACnC,IAAI,CAAC,KAAK,OAAO;IAEjB,2CAA2C;IAC3C,IAAI,CAAC,IAAI,UAAU,CAAC,cAAc,CAAC,IAAI,UAAU,CAAC,aAAa;QAC7D,OAAO,CAAC,QAAQ,EAAE,KAAK;IACzB;IAEA,OAAO;AACT;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,2EAA2E;IAC3E,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,kBAAkB,QAAgB,EAAE,WAAoB;IACtE,MAAM,OAAO,eAAe;IAC5B,OAAO,GAAG,KAAK,aAAa,CAAC;AAC/B;AAEO,SAAS,wBAAwB,GAAY,EAAE,QAAiB;IACrE,IAAI,KAAK;QACP,OAAO,aAAa,KAAK;IAC3B;IACA,OAAO,CAAC,UAAU,EAAE,SAAS,sBAAsB,CAAC;AACtD", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 active:bg-primary/95\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 active:bg-destructive/95 focus-visible:ring-destructive\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground active:bg-accent/80\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 active:bg-secondary/90\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground active:bg-accent/80\",\n        link: \"text-primary underline-offset-4 hover:underline focus-visible:ring-0 focus-visible:ring-offset-0\",\n        success:\n          \"bg-success text-success-foreground shadow-sm hover:bg-success/90 active:bg-success/95 focus-visible:ring-success\",\n        warning:\n          \"bg-warning text-warning-foreground shadow-sm hover:bg-warning/90 active:bg-warning/95 focus-visible:ring-warning\",\n        info: \"bg-info text-info-foreground shadow-sm hover:bg-info/90 active:bg-info/95 focus-visible:ring-info\",\n        gradient:\n          \"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-sm hover:from-primary/90 hover:to-primary/70 active:from-primary/95 active:to-primary/75\",\n      },\n      size: {\n        xs: \"h-7 px-2 text-xs gap-1\",\n        sm: \"h-8 px-3 text-sm gap-1.5\",\n        default: \"h-9 px-4 py-2\",\n        lg: \"h-10 px-6 text-base\",\n        xl: \"h-12 px-8 text-lg\",\n        icon: \"size-9\",\n        \"icon-sm\": \"size-8\",\n        \"icon-lg\": \"size-10\",\n        \"icon-xl\": \"size-12\",\n      },\n      animation: {\n        none: \"\",\n        hover: \"hover:scale-105 active:scale-95\",\n        lift: \"hover:-translate-y-0.5 hover:shadow-lg active:translate-y-0\",\n        glow: \"hover:shadow-lg hover:shadow-primary/25\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n      animation: \"none\",\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n  isLoading?: boolean;\n  loadingText?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  (\n    {\n      className,\n      variant,\n      size,\n      animation,\n      asChild = false,\n      isLoading = false,\n      loadingText,\n      leftIcon,\n      rightIcon,\n      children,\n      disabled,\n      ...props\n    },\n    ref\n  ) => {\n    const Comp = asChild ? Slot : \"button\";\n    const isDisabled = disabled || isLoading;\n\n    return (\n      <Comp\n        ref={ref}\n        data-slot=\"button\"\n        className={cn(buttonVariants({ variant, size, animation, className }))}\n        disabled={isDisabled}\n        aria-disabled={isDisabled}\n        {...props}\n      >\n        {isLoading && (\n          <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent\" />\n        )}\n        {!isLoading && leftIcon && leftIcon}\n        {isLoading ? loadingText || children : children}\n        {!isLoading && rightIcon && rightIcon}\n      </Comp>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,ugBACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;YACN,SACE;YACF,SACE;YACF,MAAM;YACN,UACE;QACJ;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,WAAW;YACX,WAAW;YACX,WAAW;QACb;QACA,WAAW;YACT,MAAM;YACN,OAAO;YACP,MAAM;YACN,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,WAAW;IACb;AACF;AAaF,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC5B,CACE,EACE,SAAS,EACT,OAAO,EACP,IAAI,EACJ,SAAS,EACT,UAAU,KAAK,EACf,YAAY,KAAK,EACjB,WAAW,EACX,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,aAAa,YAAY;IAE/B,qBACE,6WAAC;QACC,KAAK;QACL,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;YAAW;QAAU;QACnE,UAAU;QACV,iBAAe;QACd,GAAG,KAAK;;YAER,2BACC,6WAAC;gBAAI,WAAU;;;;;;YAEhB,CAAC,aAAa,YAAY;YAC1B,YAAY,eAAe,WAAW;YACtC,CAAC,aAAa,aAAa;;;;;;;AAGlC;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/ui/error-boundary.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport { AlertTriangle, RefreshCw, Home } from \"lucide-react\";\nimport Link from \"next/link\";\n\ninterface ErrorBoundaryState {\n  hasError: boolean;\n  error?: Error;\n  errorInfo?: React.ErrorInfo;\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode;\n  fallback?: React.ComponentType<ErrorFallbackProps>;\n  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;\n  showDetails?: boolean;\n}\n\ninterface ErrorFallbackProps {\n  error?: Error;\n  errorInfo?: React.ErrorInfo;\n  resetError: () => void;\n  showDetails?: boolean;\n}\n\nclass ErrorBoundary extends React.Component<\n  ErrorBoundaryProps,\n  ErrorBoundaryState\n> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return {\n      hasError: true,\n      error,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    this.setState({\n      error,\n      errorInfo,\n    });\n\n    // Log error to monitoring service\n    this.props.onError?.(error, errorInfo);\n\n    // In production, you might want to log to a service like Sentry\n    if (process.env.NODE_ENV === \"production\") {\n      console.error(\"Error caught by boundary:\", error, errorInfo);\n    }\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      const FallbackComponent = this.props.fallback || DefaultErrorFallback;\n\n      return (\n        <FallbackComponent\n          error={this.state.error}\n          errorInfo={this.state.errorInfo}\n          resetError={this.resetError}\n          showDetails={this.props.showDetails}\n        />\n      );\n    }\n\n    // Wrap children in a fragment to handle multiple children\n    return <>{this.props.children}</>;\n  }\n}\n\n// Default error fallback component\nfunction DefaultErrorFallback({\n  error,\n  errorInfo,\n  resetError,\n  showDetails = false,\n}: ErrorFallbackProps) {\n  const [showErrorDetails, setShowErrorDetails] = React.useState(false);\n\n  return (\n    <div className=\"min-h-[400px] flex items-center justify-center p-6\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"text-center\">\n          <div className=\"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10\">\n            <AlertTriangle className=\"h-6 w-6 text-destructive\" />\n          </div>\n          <CardTitle className=\"text-xl\">Something went wrong</CardTitle>\n          <CardDescription>\n            We encountered an unexpected error. Please try refreshing the page\n            or contact support if the problem persists.\n          </CardDescription>\n        </CardHeader>\n\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex flex-col gap-2\">\n            <Button onClick={resetError} className=\"w-full\">\n              <RefreshCw className=\"mr-2 h-4 w-4\" />\n              Try Again\n            </Button>\n\n            <Button variant=\"outline\" asChild className=\"w-full\">\n              <Link href=\"/\">\n                <Home className=\"mr-2 h-4 w-4\" />\n                Go Home\n              </Link>\n            </Button>\n          </div>\n\n          {showDetails && (\n            <div className=\"space-y-2\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setShowErrorDetails(!showErrorDetails)}\n                className=\"w-full text-xs\"\n              >\n                {showErrorDetails ? \"Hide\" : \"Show\"} Error Details\n              </Button>\n\n              {showErrorDetails && (\n                <div className=\"rounded-md bg-muted p-3 text-xs font-mono\">\n                  <div className=\"mb-2 font-semibold\">Error:</div>\n                  <div className=\"mb-3 text-destructive\">\n                    {error?.message || \"Unknown error\"}\n                  </div>\n\n                  {error?.stack && (\n                    <>\n                      <div className=\"mb-2 font-semibold\">Stack Trace:</div>\n                      <pre className=\"whitespace-pre-wrap text-xs opacity-70\">\n                        {error.stack}\n                      </pre>\n                    </>\n                  )}\n\n                  {errorInfo?.componentStack && (\n                    <>\n                      <div className=\"mb-2 mt-3 font-semibold\">\n                        Component Stack:\n                      </div>\n                      <pre className=\"whitespace-pre-wrap text-xs opacity-70\">\n                        {errorInfo.componentStack}\n                      </pre>\n                    </>\n                  )}\n                </div>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n\n// Specialized error boundaries for different contexts\ninterface PageErrorBoundaryProps extends Omit<ErrorBoundaryProps, \"fallback\"> {\n  children: React.ReactNode;\n}\n\nexport function PageErrorBoundary({\n  children,\n  ...props\n}: PageErrorBoundaryProps) {\n  return (\n    <ErrorBoundary\n      fallback={({ error, resetError }) => (\n        <div className=\"min-h-screen flex items-center justify-center p-6 bg-background\">\n          <Card className=\"w-full max-w-lg\">\n            <CardHeader className=\"text-center\">\n              <div className=\"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10\">\n                <AlertTriangle className=\"h-8 w-8 text-destructive\" />\n              </div>\n              <CardTitle className=\"text-2xl\">Page Error</CardTitle>\n              <CardDescription className=\"text-base\">\n                This page encountered an error and couldn't load properly.\n              </CardDescription>\n            </CardHeader>\n\n            <CardContent className=\"space-y-4\">\n              <div className=\"flex flex-col gap-3\">\n                <Button onClick={resetError} size=\"lg\" className=\"w-full\">\n                  <RefreshCw className=\"mr-2 h-4 w-4\" />\n                  Reload Page\n                </Button>\n\n                <Button variant=\"outline\" size=\"lg\" asChild className=\"w-full\">\n                  <Link href=\"/\">\n                    <Home className=\"mr-2 h-4 w-4\" />\n                    Return Home\n                  </Link>\n                </Button>\n              </div>\n\n              {process.env.NODE_ENV === \"development\" && (\n                <details className=\"mt-4\">\n                  <summary className=\"cursor-pointer text-sm text-muted-foreground hover:text-foreground\">\n                    Error Details (Development)\n                  </summary>\n                  <pre className=\"mt-2 rounded-md bg-muted p-3 text-xs overflow-auto\">\n                    {error?.stack || error?.message || \"Unknown error\"}\n                  </pre>\n                </details>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n      )}\n      {...props}\n    >\n      {children}\n    </ErrorBoundary>\n  );\n}\n\n// Component error boundary for smaller components\nexport function ComponentErrorBoundary({\n  children,\n  ...props\n}: PageErrorBoundaryProps) {\n  return (\n    <ErrorBoundary\n      fallback={({ error, resetError }) => (\n        <div className=\"rounded-lg border border-destructive/20 bg-destructive/5 p-4\">\n          <div className=\"flex items-center gap-2 mb-2\">\n            <AlertTriangle className=\"h-4 w-4 text-destructive\" />\n            <span className=\"text-sm font-medium text-destructive\">\n              Component Error\n            </span>\n          </div>\n          <p className=\"text-xs text-muted-foreground mb-3\">\n            This component failed to render properly.\n          </p>\n          <Button size=\"sm\" variant=\"outline\" onClick={resetError}>\n            <RefreshCw className=\"mr-1 h-3 w-3\" />\n            Retry\n          </Button>\n\n          {process.env.NODE_ENV === \"development\" && (\n            <details className=\"mt-3\">\n              <summary className=\"cursor-pointer text-xs text-muted-foreground\">\n                Debug Info\n              </summary>\n              <pre className=\"mt-1 text-xs bg-background rounded p-2 overflow-auto\">\n                {error?.message || \"Unknown error\"}\n              </pre>\n            </details>\n          )}\n        </div>\n      )}\n      {...props}\n    >\n      {children}\n    </ErrorBoundary>\n  );\n}\n\n// Hook for handling async errors in components\nexport function useErrorHandler() {\n  const [error, setError] = React.useState<Error | null>(null);\n\n  const resetError = React.useCallback(() => {\n    setError(null);\n  }, []);\n\n  const handleError = React.useCallback((error: Error) => {\n    setError(error);\n  }, []);\n\n  React.useEffect(() => {\n    if (error) {\n      throw error;\n    }\n  }, [error]);\n\n  return { handleError, resetError };\n}\n\nexport { ErrorBoundary, DefaultErrorFallback };\nexport type { ErrorBoundaryProps, ErrorFallbackProps };\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AAOA;AAAA;AAAA;AACA;AAZA;;;;;;;AAkCA,MAAM,sBAAsB,oUAAA,CAAA,YAAe;IAIzC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,IAAI,CAAC,QAAQ,CAAC;YACZ;YACA;QACF;QAEA,kCAAkC;QAClC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO;QAE5B,gEAAgE;QAChE,uCAA2C;;QAE3C;IACF;IAEA,aAAa;QACX,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;YAAW,WAAW;QAAU;IAC1E,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI;YAEjD,qBACE,6WAAC;gBACC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBACvB,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS;gBAC/B,YAAY,IAAI,CAAC,UAAU;gBAC3B,aAAa,IAAI,CAAC,KAAK,CAAC,WAAW;;;;;;QAGzC;QAEA,0DAA0D;QAC1D,qBAAO;sBAAG,IAAI,CAAC,KAAK,CAAC,QAAQ;;IAC/B;AACF;AAEA,mCAAmC;AACnC,SAAS,qBAAqB,EAC5B,KAAK,EACL,SAAS,EACT,UAAU,EACV,cAAc,KAAK,EACA;IACnB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAc,AAAD,EAAE;IAE/D,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC,yHAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6WAAC,yHAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC,4SAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAE3B,6WAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;sCAC/B,6WAAC,yHAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAMnB,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,2HAAA,CAAA,SAAM;oCAAC,SAAS;oCAAY,WAAU;;sDACrC,6WAAC,oSAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIxC,6WAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,OAAO;oCAAC,WAAU;8CAC1C,cAAA,6WAAC,2RAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6WAAC,uRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;wBAMtC,6BACC,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,2HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;;wCAET,mBAAmB,SAAS;wCAAO;;;;;;;gCAGrC,kCACC,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,6WAAC;4CAAI,WAAU;sDACZ,OAAO,WAAW;;;;;;wCAGpB,OAAO,uBACN;;8DACE,6WAAC;oDAAI,WAAU;8DAAqB;;;;;;8DACpC,6WAAC;oDAAI,WAAU;8DACZ,MAAM,KAAK;;;;;;;;wCAKjB,WAAW,gCACV;;8DACE,6WAAC;oDAAI,WAAU;8DAA0B;;;;;;8DAGzC,6WAAC;oDAAI,WAAU;8DACZ,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYjD;AAOO,SAAS,kBAAkB,EAChC,QAAQ,EACR,GAAG,OACoB;IACvB,qBACE,6WAAC;QACC,UAAU,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,iBAC9B,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,yHAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6WAAC,yHAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC,4SAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,6WAAC,yHAAA,CAAA,YAAS;oCAAC,WAAU;8CAAW;;;;;;8CAChC,6WAAC,yHAAA,CAAA,kBAAe;oCAAC,WAAU;8CAAY;;;;;;;;;;;;sCAKzC,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,2HAAA,CAAA,SAAM;4CAAC,SAAS;4CAAY,MAAK;4CAAK,WAAU;;8DAC/C,6WAAC,oSAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAIxC,6WAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,OAAO;4CAAC,WAAU;sDACpD,cAAA,6WAAC,2RAAA,CAAA,UAAI;gDAAC,MAAK;;kEACT,6WAAC,uRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;gCAMtC,oDAAyB,+BACxB,6WAAC;oCAAQ,WAAU;;sDACjB,6WAAC;4CAAQ,WAAU;sDAAqE;;;;;;sDAGxF,6WAAC;4CAAI,WAAU;sDACZ,OAAO,SAAS,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAQhD,GAAG,KAAK;kBAER;;;;;;AAGP;AAGO,SAAS,uBAAuB,EACrC,QAAQ,EACR,GAAG,OACoB;IACvB,qBACE,6WAAC;QACC,UAAU,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,iBAC9B,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,4SAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,6WAAC;gCAAK,WAAU;0CAAuC;;;;;;;;;;;;kCAIzD,6WAAC;wBAAE,WAAU;kCAAqC;;;;;;kCAGlD,6WAAC,2HAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAQ;wBAAU,SAAS;;0CAC3C,6WAAC,oSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;oBAIvC,oDAAyB,+BACxB,6WAAC;wBAAQ,WAAU;;0CACjB,6WAAC;gCAAQ,WAAU;0CAA+C;;;;;;0CAGlE,6WAAC;gCAAI,WAAU;0CACZ,OAAO,WAAW;;;;;;;;;;;;;;;;;;QAM5B,GAAG,KAAK;kBAER;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAc,AAAD,EAAgB;IAEvD,MAAM,aAAa,CAAA,GAAA,oUAAA,CAAA,cAAiB,AAAD,EAAE;QACnC,SAAS;IACX,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAiB,AAAD,EAAE,CAAC;QACrC,SAAS;IACX,GAAG,EAAE;IAEL,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,OAAO;YACT,MAAM;QACR;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QAAE;QAAa;IAAW;AACnC", "debugId": null}}]}