"use client";

import { useState } from "react";
import { Link, ColorSettings } from "@/types";
import { formatUrl } from "@/lib/utils";
import { cn } from "@/lib/utils";

interface LinkCardProps {
  link: Link;
  colors?: ColorSettings;
  className?: string;
}

export function LinkCard({ link, colors, className }: LinkCardProps) {
  const [isClicked, setIsClicked] = useState(false);

  const handleClick = async () => {
    setIsClicked(true);

    // Open link in new tab
    window.open(formatUrl(link.url), "_blank", "noopener,noreferrer");

    // Reset click state after animation
    setTimeout(() => setIsClicked(false), 200);
  };

  const getButtonStyle = () => {
    const baseStyle =
      "w-full p-4 text-left transition-all duration-200 hover:scale-105 active:scale-95 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg";

    return cn(baseStyle, isClicked && "scale-95");
  };

  const buttonStyle = {
    backgroundColor: colors?.primary || "#ffffff",
    color: colors?.linkText || "#000000",
    border: `1px solid ${colors?.secondary || "#e5e7eb"}`,
  };

  return (
    <button
      onClick={handleClick}
      className={cn(getButtonStyle(), className)}
      style={buttonStyle}
      aria-label={`Visit ${link.text}`}
    >
      <div className="flex items-center space-x-3">
        {link.classIcon && link.classIcon !== "#" && (
          <div className="shrink-0 w-6 h-6 flex items-center justify-center">
            <i className={`${link.classIcon} text-lg`} />
          </div>
        )}

        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-sm sm:text-base truncate">
            {link.text}
          </h3>
        </div>

        <div className="shrink-0">
          <svg
            className="w-4 h-4 opacity-50"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
            />
          </svg>
        </div>
      </div>
    </button>
  );
}
