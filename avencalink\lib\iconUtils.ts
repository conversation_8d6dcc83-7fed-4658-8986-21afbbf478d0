import {
    Facebook,
    Twitter,
    Instagram,
    Linkedin,
    Youtube,
    Github,
    Globe,
    Mail,
    Phone,
    MessageCircle,
    Share2,
    ExternalLink,
} from "lucide-react";

/**
 * Maps icon names to their corresponding Lucide React components
 * Handles various icon name formats including Font Awesome prefixes
 *
 * @param iconName - The icon name to map (supports fa-, fab-, fas-, far- prefixes)
 * @returns The corresponding React component or ExternalLink as fallback
 */
export const getIconComponent = (iconName: string): React.ComponentType<React.SVGProps<SVGSVGElement>> => {
    const iconMap: Record<
        string,
        React.ComponentType<React.SVGProps<SVGSVGElement>>
    > = {
        facebook: Facebook,
        twitter: Twitter,
        instagram: Instagram,
        linkedin: Linkedin,
        youtube: Youtube,
        github: Github,
        globe: Globe,
        website: Globe,
        mail: Mail,
        email: Mail,
        phone: Phone,
        whatsapp: MessageCircle,
        telegram: MessageCircle,
        share: Share2,
        link: ExternalLink,
        external: ExternalLink,
    };

    // Clean the icon name by removing Font Awesome prefixes and converting to lowercase
    // Handle formats like "fa fa-whatsapp", "fab fa-instagram", "fas fa-phone", etc.
    const cleanName = iconName
        .replace(
            /^(fa\s+fa-|fab\s+fa-|fas\s+fa-|far\s+fa-|icon-|fa-|fab-|fas-|far-)/i,
            ""
        )
        .toLowerCase()
        .trim();

    return iconMap[cleanName] || ExternalLink;
};