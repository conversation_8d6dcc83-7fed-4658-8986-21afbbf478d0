import { NextRequest, NextResponse } from 'next/server'

// Mock analytics storage - in a real app, this would be your database
const mockLinkClicks: Record<string, { clicks: number; lastClicked: string }> = {}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ linkId: string }> }
): Promise<NextResponse> {
  try {
    const { linkId } = await params

    // Validate linkId
    if (!linkId || linkId.trim() === '') {
      return NextResponse.json(
        { success: false, error: 'Invalid link ID' },
        { status: 400 }
      )
    }

    // Get client IP for analytics
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown'

    // Track the click
    if (!mockLinkClicks[linkId]) {
      mockLinkClicks[linkId] = { clicks: 0, lastClicked: '' }
    }

    mockLinkClicks[linkId].clicks += 1
    mockLinkClicks[linkId].lastClicked = new Date().toISOString()

    console.log(`Link click tracked for ${linkId} from IP: ${clientIP}`)

    return NextResponse.json({
      success: true,
      message: 'Click tracked successfully',
      data: {
        linkId,
        totalClicks: mockLinkClicks[linkId].clicks,
      },
    })
  } catch (error) {
    console.error('Error tracking link click:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
