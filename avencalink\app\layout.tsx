import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AvencaLink - One link to share everything",
  description:
    "Create a beautiful landing page for all your links. Share your content, social media, and more with just one link.",
  keywords: ["linktree", "bio link", "link in bio", "social media", "links"],
  authors: [{ name: "Avenca Digital" }],
  creator: "Avenca Digital",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://avencalink.com",
    title: "AvencaLink - One link to share everything",
    description:
      "Create a beautiful landing page for all your links. Share your content, social media, and more with just one link.",
    siteName: "AvencaLink",
  },
  twitter: {
    card: "summary_large_image",
    title: "AvencaLink - One link to share everything",
    description:
      "Create a beautiful landing page for all your links. Share your content, social media, and more with just one link.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
