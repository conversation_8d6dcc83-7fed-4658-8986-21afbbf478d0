import { ColorSettings } from '@/types/user'
import { DEFAULT_COLOR_SCHEMES } from '@/types/constants'

/**
 * Validates if a color string is a valid hex color
 */
export function isValidHexColor(color: string): boolean {
  if (!color || typeof color !== 'string') return false
  
  // Remove # if present
  const cleanColor = color.replace('#', '')
  
  // Check if it's a valid hex color (3, 6, or 8 characters)
  const hexRegex = /^[0-9A-Fa-f]{3}$|^[0-9A-Fa-f]{6}$|^[0-9A-Fa-f]{8}$/
  return hexRegex.test(cleanColor)
}

/**
 * Validates if a color string is a valid CSS color (hex, rgb, rgba, hsl, etc.)
 */
export function isValidCSSColor(color: string): boolean {
  if (!color || typeof color !== 'string') return false
  
  // Check for hex colors
  if (color.startsWith('#')) {
    return isValidHexColor(color)
  }
  
  // Check for rgb/rgba colors
  if (color.startsWith('rgb')) {
    const rgbRegex = /^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(?:,\s*[\d.]+)?\s*\)$/
    return rgbRegex.test(color)
  }
  
  // Check for hsl/hsla colors
  if (color.startsWith('hsl')) {
    const hslRegex = /^hsla?\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*(?:,\s*[\d.]+)?\s*\)$/
    return hslRegex.test(color)
  }
  
  // Check for named colors (basic validation)
  const namedColors = [
    'transparent', 'black', 'white', 'red', 'green', 'blue', 'yellow', 
    'orange', 'purple', 'pink', 'gray', 'grey', 'brown', 'cyan', 'magenta'
  ]
  return namedColors.includes(color.toLowerCase())
}

/**
 * Sanitizes and validates color settings, providing fallbacks for invalid colors
 */
export function sanitizeColorSettings(colors: Partial<ColorSettings> | undefined): ColorSettings {
  const defaultColors = DEFAULT_COLOR_SCHEMES.DARK
  
  if (!colors) {
    return defaultColors
  }
  
  return {
    background: isValidCSSColor(colors.background || '') 
      ? colors.background! 
      : defaultColors.background,
    linkText: isValidCSSColor(colors.linkText || '') 
      ? colors.linkText! 
      : defaultColors.linkText,
    primary: isValidCSSColor(colors.primary || '') 
      ? colors.primary! 
      : defaultColors.primary,
    secondary: isValidCSSColor(colors.secondary || '') 
      ? colors.secondary! 
      : defaultColors.secondary,
    socialIconBackground: isValidCSSColor(colors.socialIconBackground || '') 
      ? colors.socialIconBackground! 
      : defaultColors.socialIconBackground,
  }
}

/**
 * Creates color variations with opacity for hover and active states
 */
export function createColorVariations(baseColor: string) {
  if (!isValidHexColor(baseColor)) {
    return {
      base: baseColor,
      hover: baseColor,
      active: baseColor,
      disabled: baseColor,
    }
  }
  
  // Remove # if present
  const cleanColor = baseColor.replace('#', '')
  
  // Convert 3-digit hex to 6-digit
  const fullHex = cleanColor.length === 3 
    ? cleanColor.split('').map(char => char + char).join('')
    : cleanColor
  
  return {
    base: `#${fullHex}`,
    hover: `#${fullHex}E6`, // 90% opacity
    active: `#${fullHex}CC`, // 80% opacity
    disabled: `#${fullHex}66`, // 40% opacity
    light: `#${fullHex}33`, // 20% opacity
    extraLight: `#${fullHex}1A`, // 10% opacity
  }
}

/**
 * Gets the appropriate text color (black or white) based on background color
 */
export function getContrastTextColor(backgroundColor: string): string {
  if (!isValidHexColor(backgroundColor)) {
    return '#ffffff' // Default to white for invalid colors
  }
  
  // Remove # if present
  const cleanColor = backgroundColor.replace('#', '')
  
  // Convert 3-digit hex to 6-digit
  const fullHex = cleanColor.length === 3 
    ? cleanColor.split('').map(char => char + char).join('')
    : cleanColor
  
  // Convert hex to RGB
  const r = parseInt(fullHex.substr(0, 2), 16)
  const g = parseInt(fullHex.substr(2, 2), 16)
  const b = parseInt(fullHex.substr(4, 2), 16)
  
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
  
  // Return black for light backgrounds, white for dark backgrounds
  return luminance > 0.5 ? '#000000' : '#ffffff'
}

/**
 * Creates a complete color theme with all necessary variations
 */
export function createColorTheme(colors: Partial<ColorSettings> | undefined) {
  const sanitizedColors = sanitizeColorSettings(colors)
  
  return {
    background: sanitizedColors.background,
    foreground: getContrastTextColor(sanitizedColors.background),
    primary: {
      ...createColorVariations(sanitizedColors.primary),
      foreground: getContrastTextColor(sanitizedColors.primary),
    },
    secondary: {
      ...createColorVariations(sanitizedColors.secondary),
      foreground: getContrastTextColor(sanitizedColors.secondary),
    },
    socialIcon: {
      ...createColorVariations(sanitizedColors.socialIconBackground),
      foreground: getContrastTextColor(sanitizedColors.socialIconBackground),
    },
    linkText: sanitizedColors.linkText,
  }
}

/**
 * Applies color theme to CSS custom properties
 */
export function applyColorTheme(colors: Partial<ColorSettings> | undefined): React.CSSProperties {
  const theme = createColorTheme(colors)
  
  return {
    '--color-background': theme.background,
    '--color-foreground': theme.foreground,
    '--color-primary': theme.primary.base,
    '--color-primary-hover': theme.primary.hover,
    '--color-primary-active': theme.primary.active,
    '--color-primary-foreground': theme.primary.foreground,
    '--color-secondary': theme.secondary.base,
    '--color-secondary-hover': theme.secondary.hover,
    '--color-secondary-active': theme.secondary.active,
    '--color-secondary-foreground': theme.secondary.foreground,
    '--color-social-icon': theme.socialIcon.base,
    '--color-social-icon-hover': theme.socialIcon.hover,
    '--color-social-icon-foreground': theme.socialIcon.foreground,
    '--color-link-text': theme.linkText,
  } as React.CSSProperties
}
