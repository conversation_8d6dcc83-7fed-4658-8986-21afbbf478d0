import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Separator } from "@/components/ui/separator";
import { useState, useEffect } from "react";
import { MapPin } from "lucide-react";
import { createColorVariations, isValidCSSColor } from "@/lib/colorUtils";

interface UserProfileProps {
  user: {
    avatar: string;
    name: string;
    username: string;
    bio: string;
  };
  colors?: {
    background?: string;
    linkText?: string;
    primary?: string;
    secondary?: string;
    socialIconBackground?: string;
  };
}

const UserProfile = ({ user, colors }: UserProfileProps) => {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  // Simulate image loading
  useEffect(() => {
    const img = new Image();
    img.onload = () => setIsImageLoaded(true);
    img.src = user.avatar;
  }, [user.avatar]);

  const specialties = ["Traços Finos", "Realismo", "Floral", "Minimalista"];

  return (
    <TooltipProvider>
      <div className="text-center mb-8 animate-fade-in">
        {/* Enhanced Avatar Section */}
        <div className="relative inline-block mb-6 group">
          <div className="relative">
            {/* Avatar with loading state and enhanced effects */}
            <Avatar
              className="w-24 h-24 sm:w-28 sm:h-28 lg:w-32 lg:h-32 mx-auto shadow-strong ring-4 transition-all duration-500 hover:ring-8 hover:shadow-2xl group-hover:scale-105"
              style={
                colors?.primary && isValidCSSColor(colors.primary)
                  ? (() => {
                      const colorVariations = createColorVariations(
                        colors.primary
                      );
                      return {
                        "--ring-color": colorVariations.light,
                        "--ring-hover-color": colorVariations.base,
                      } as React.CSSProperties;
                    })()
                  : {}
              }
            >
              <AvatarImage
                src={user.avatar}
                alt={user.name}
                className={`object-cover transition-all duration-500 ${
                  isImageLoaded ? "opacity-100 scale-100" : "opacity-0 scale-95"
                } group-hover:scale-110`}
                onLoad={() => setIsImageLoaded(true)}
              />
              <AvatarFallback
                className="text-2xl sm:text-3xl lg:text-4xl font-bold animate-pulse"
                style={
                  colors?.primary && isValidCSSColor(colors.primary)
                    ? {
                        backgroundColor: colors.primary,
                        color: colors.linkText || "#ffffff",
                      }
                    : {}
                }
              >
                {user.name.charAt(0)}
              </AvatarFallback>
            </Avatar>

            {/* Enhanced online indicator with tooltip */}
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 sm:w-7 sm:h-7 bg-green-500 rounded-full border-4 border-background animate-pulse cursor-help shadow-lg">
                  <div className="absolute inset-0 bg-green-400 rounded-full animate-ping opacity-75"></div>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs font-medium">Ativo agora</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>

        {/* Enhanced Name Section */}
        <div className="mb-4">
          <div className="flex items-center justify-center gap-2 mb-2">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground tracking-tight bg-clip-text">
              {user.name}
            </h1>
          </div>

          {/* Username with copy functionality */}
          <div className="flex items-center justify-center gap-2 mb-3">
            <p className="text-sm sm:text-base text-muted-foreground font-medium">
              @{user.username}
            </p>
            <div className="flex items-center gap-1 text-muted-foreground">
              <MapPin className="w-3 h-3" />
              <span className="text-xs">Palmeira das Missões</span>
            </div>
          </div>

          {/* Specialties badges */}
          <div className="flex flex-wrap justify-center gap-2 mb-4">
            {specialties.map((specialty, index) => (
              <Badge
                key={specialty}
                variant="secondary"
                className="text-xs px-3 py-1 transition-colors duration-300 rounded-3xl"
                style={
                  colors?.primary && isValidCSSColor(colors.primary)
                    ? (() => {
                        const colorVariations = createColorVariations(
                          colors.primary
                        );
                        return {
                          backgroundColor: colorVariations.extraLight,
                          color: colors.primary,
                          borderColor: colorVariations.light,
                          animationDelay: `${index * 0.1}s`,
                          "--hover-bg": colorVariations.light,
                        } as React.CSSProperties;
                      })()
                    : { animationDelay: `${index * 0.1}s` }
                }
                onMouseEnter={(e) => {
                  if (colors?.primary && isValidCSSColor(colors.primary)) {
                    const colorVariations = createColorVariations(
                      colors.primary
                    );
                    e.currentTarget.style.backgroundColor =
                      colorVariations.light;
                  }
                }}
                onMouseLeave={(e) => {
                  if (colors?.primary && isValidCSSColor(colors.primary)) {
                    const colorVariations = createColorVariations(
                      colors.primary
                    );
                    e.currentTarget.style.backgroundColor =
                      colorVariations.extraLight;
                  }
                }}
              >
                {specialty}
              </Badge>
            ))}
          </div>
        </div>

        {/* Enhanced Bio Section */}
        <Card className="max-w-md sm:max-w-lg lg:max-w-xl mx-auto mb-6 bg-card/20 backdrop-blur-sm border-border/50 shadow-none hover:shadow-xl transition-all duration-300">
          <CardContent className="p-4 sm:p-6">
            <p className="text-sm sm:text-base lg:text-lg text-foreground leading-relaxed sm:leading-loose text-center">
              {user.bio}
            </p>
          </CardContent>
        </Card>

        {/* Enhanced Decorative Elements */}
        <div className="flex items-center justify-center gap-4 mb-4">
          <Separator
            className="flex-1 max-w-16 opacity-60"
            style={
              colors?.primary && isValidCSSColor(colors.primary)
                ? {
                    background: `linear-gradient(to right, ${colors.primary}, ${colors.primary}CC)`,
                  }
                : {}
            }
          />
          <div className="flex items-center gap-1">
            <div
              className="w-2 h-2 rounded-full animate-pulse"
              style={{
                backgroundColor:
                  colors?.primary && isValidCSSColor(colors.primary)
                    ? colors.primary
                    : undefined,
              }}
            ></div>
            <div
              className="w-1.5 h-1.5 rounded-full animate-pulse"
              style={{
                animationDelay: "0.5s",
                backgroundColor:
                  colors?.primary && isValidCSSColor(colors.primary)
                    ? `${colors.primary}B3` // 70% opacity
                    : undefined,
              }}
            ></div>
            <div
              className="w-1 h-1 rounded-full animate-pulse"
              style={{
                animationDelay: "1s",
                backgroundColor:
                  colors?.primary && isValidCSSColor(colors.primary)
                    ? `${colors.primary}80` // 50% opacity
                    : undefined,
              }}
            ></div>
          </div>
          <Separator
            className="flex-1 max-w-16 opacity-60"
            style={
              colors?.primary && isValidCSSColor(colors.primary)
                ? {
                    background: `linear-gradient(to right, ${colors.primary}, ${colors.primary}CC)`,
                  }
                : {}
            }
          />
        </div>
      </div>
    </TooltipProvider>
  );
};

export default UserProfile;
